package dk.myckasP.mythicvaults.gui;

import dk.myckasP.mythicvaults.MythicVaults;
import dk.myckasP.mythicvaults.models.Crate;
import dk.myckasP.mythicvaults.models.CrateReward;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PreviewGUI implements InventoryHolder {
    private final MythicVaults plugin;
    private final Player player;
    private final Crate crate;
    private final Inventory inventory;

    public PreviewGUI(MythicVaults plugin, Player player, Crate crate) {
        this.plugin = plugin;
        this.player = player;
        this.crate = crate;
        this.inventory = Bukkit.createInventory(this, 54, 
            ChatColor.DARK_GRAY + "Preview: " + crate.getType().getDisplayName());
        
        setupPreview();
    }

    private void setupPreview() {
        List<CrateReward> crateRewards = crate.getRewards();
        int rewardCount = crateRewards.size();

        // Fill inventory with rewards showing percentages in lore
        for (int i = 0; i < Math.min(crateRewards.size(), 45); i++) {
            CrateReward reward = crateRewards.get(i);
            ItemStack displayItem = createPreviewItem(reward);
            inventory.setItem(i, displayItem);
        }

        // Add info item at bottom
        ItemStack infoItem = new ItemStack(Material.BOOK);
        ItemMeta meta = infoItem.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(crate.getType().getDisplayName() + ChatColor.RESET + " Information");
            meta.setLore(Arrays.asList(
                ChatColor.GRAY + "This crate contains " + rewardCount + " different rewards.",
                ChatColor.GRAY + "Right-click the crate to open it!",
                ChatColor.GRAY + "You can win 3 items per opening.",
                "",
                ChatColor.YELLOW + "Click anywhere to close this preview."
            ));
            infoItem.setItemMeta(meta);
        }
        inventory.setItem(49, infoItem);
        
        // Fill empty slots with gray glass
        ItemStack grayGlass = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta glassMeta = grayGlass.getItemMeta();
        if (glassMeta != null) {
            glassMeta.setDisplayName(" ");
            grayGlass.setItemMeta(glassMeta);
        }
        
        for (int i = 45; i < 54; i++) {
            if (i != 49) {
                inventory.setItem(i, grayGlass);
            }
        }
    }

    public void open() {
        player.openInventory(inventory);
    }

    @Override
    public Inventory getInventory() {
        return inventory;
    }

    public Player getPlayer() {
        return player;
    }

    public Crate getCrate() {
        return crate;
    }

    /**
     * Creates a preview item with percentage information added to the lore
     * @param reward The crate reward to create a preview item for
     * @return ItemStack with percentage information in lore
     */
    private ItemStack createPreviewItem(CrateReward reward) {
        ItemStack item = reward.getDisplayItem().clone();
        ItemMeta meta = item.getItemMeta();

        if (meta != null) {
            List<String> lore = meta.getLore();
            if (lore == null) {
                lore = new ArrayList<>();
            }

            // Filter out "Right-click to open" text from lore
            lore = filterRightClickText(lore);

            // Add percentage information to the lore
            lore.add("");
            lore.add(ChatColor.GOLD + "Drop Chance: " + formatPercentage(reward.getChance()) + "%");

            meta.setLore(lore);
            item.setItemMeta(meta);
        }

        return item;
    }

    /**
     * Filters out "Right-click to open" text from item lore
     * @param lore The original lore list
     * @return Filtered lore list without right-click text
     */
    private List<String> filterRightClickText(List<String> lore) {
        List<String> filteredLore = new ArrayList<>();
        for (String line : lore) {
            // Remove color codes for comparison
            String plainLine = ChatColor.stripColor(line).toLowerCase();
            if (!plainLine.contains("right-click to open") && !plainLine.contains("right click to open")) {
                filteredLore.add(line);
            }
        }
        return filteredLore;
    }

    /**
     * Formats the percentage to show appropriate decimal places
     * @param chance The chance value to format
     * @return Formatted percentage string
     */
    private String formatPercentage(double chance) {
        // If it's a whole number, show no decimals
        if (chance == Math.floor(chance)) {
            return String.format("%.0f", chance);
        }
        // If it has one decimal place, show one decimal
        else if (chance * 10 == Math.floor(chance * 10)) {
            return String.format("%.1f", chance);
        }
        // Otherwise show two decimal places
        else {
            return String.format("%.2f", chance);
        }
    }
}
