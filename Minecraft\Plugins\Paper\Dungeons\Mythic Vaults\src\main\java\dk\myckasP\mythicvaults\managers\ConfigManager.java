package dk.myckasP.mythicvaults.managers;

import dk.myckasP.mythicvaults.MythicVaults;
import org.bukkit.configuration.file.FileConfiguration;

public class ConfigManager {
    private final MythicVaults plugin;
    private FileConfiguration config;

    public ConfigManager(MythicVaults plugin) {
        this.plugin = plugin;
    }

    public void loadConfig() {
        plugin.saveDefaultConfig();
        plugin.reloadConfig();
        config = plugin.getConfig();
    }

    public FileConfiguration getConfig() {
        return config;
    }

    public void reloadConfig() {
        plugin.reloadConfig();
        config = plugin.getConfig();

        // Recreate holograms with new configuration
        plugin.getHologramManager().recreateAllHolograms();
    }

    public String getMessage(String path) {
        return config.getString("messages." + path, "Message not found: " + path);
    }

    public String getMessage(String path, String defaultMessage) {
        return config.getString("messages." + path, defaultMessage);
    }
}
