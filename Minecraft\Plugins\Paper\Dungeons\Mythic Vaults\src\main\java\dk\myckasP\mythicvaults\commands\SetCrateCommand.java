package dk.myckasP.mythicvaults.commands;

import dk.myckasP.mythicvaults.MythicVaults;
import dk.myckasP.mythicvaults.enums.CrateType;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.block.Block;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class SetCrateCommand implements CommandExecutor, TabCompleter {
    private final MythicVaults plugin;

    public SetCrateCommand(MythicVaults plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players!");
            return true;
        }

        if (!sender.hasPermission("mythicvaults.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command!");
            return true;
        }

        if (args.length != 1) {
            sender.sendMessage(ChatColor.RED + "Usage: /setcrate <cratetype>");
            sender.sendMessage(ChatColor.GRAY + "Available types: " + Arrays.toString(CrateType.values()));
            return true;
        }

        Player player = (Player) sender;
        CrateType crateType = CrateType.fromString(args[0]);

        if (crateType == null) {
            player.sendMessage(ChatColor.RED + "Invalid crate type! Available types:");
            for (CrateType type : CrateType.values()) {
                player.sendMessage(ChatColor.GRAY + "- " + type.name().toLowerCase());
            }
            return true;
        }

        // Get the block the player is looking at
        Block targetBlock = player.getTargetBlockExact(10);
        if (targetBlock == null || targetBlock.getType().isAir()) {
            player.sendMessage(ChatColor.RED + "You must be looking at a block to set it as a crate!");
            return true;
        }

        Location blockLocation = targetBlock.getLocation();

        // Check if there's already a crate at this location
        if (plugin.getCrateManager().getCrate(blockLocation) != null) {
            player.sendMessage(ChatColor.YELLOW + "There's already a crate at this location! Replacing it with " + 
                crateType.getDisplayName() + ChatColor.YELLOW + ".");
        }

        // Set the crate
        plugin.getCrateManager().addCrate(blockLocation, crateType);

        player.sendMessage(ChatColor.GREEN + "Successfully set " + crateType.getDisplayName() + 
            ChatColor.GREEN + " at " + blockLocation.getBlockX() + ", " + 
            blockLocation.getBlockY() + ", " + blockLocation.getBlockZ() + "!");

        return true;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!sender.hasPermission("mythicvaults.admin")) {
            return new ArrayList<>();
        }

        if (args.length == 1) {
            List<String> completions = new ArrayList<>();
            String input = args[0].toLowerCase();
            
            for (CrateType type : CrateType.values()) {
                if (type.name().toLowerCase().startsWith(input)) {
                    completions.add(type.name().toLowerCase());
                }
            }
            return completions;
        }

        return new ArrayList<>();
    }
}
