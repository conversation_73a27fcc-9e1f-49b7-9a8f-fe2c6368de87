# Mythic Vaults Crate Configuration
# Configure rewards for each crate type

rewards:
  uncommon:
    # Keys
    uncommon_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &2&lUNCOMMON &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 6.0
      quantity: 1

    rare_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &3&lRARE &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 10.0
      quantity: 1

    common_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &F&LCOMMON &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 20.0
      quantity: 1

    # Gold Pouches
    small_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eSmall Gold Pouch"
      lore:
        - "&7Contains a small amount of gold"
        - "&8Right-click to open"
      chance: 50.0
      quantity: 1

    medium_gold_pouch:
      material: GOLD_INGOT
      displayName: "&6Medium Gold Pouch"
      lore:
        - "&7Contains a moderate amount of gold"
        - "&8Right-click to open"
      chance: 25.0
      quantity: 1

    large_gold_pouch:
      material: GOLD_INGOT
      displayName: "&4Large Gold Pouch"
      lore:
        - "&7Contains a large amount of gold"
        - "&8Right-click to open"
      chance: 5.0
      quantity: 1

    # Token Pouches
    small_token_pouch:
      material: DIAMOND
      displayName: "&bSmall Token Pouch"
      lore:
        - "&7Contains a small amount of tokens"
        - "&8Right-click to open"
      chance: 50.0
      quantity: 1

    medium_token_pouch:
      material: DIAMOND
      displayName: "&9Medium Token Pouch"
      lore:
        - "&7Contains a moderate amount of tokens"
        - "&8Right-click to open"
      chance: 25.0
      quantity: 1

    large_token_pouch:
      material: DIAMOND
      displayName: "&4Large Token Pouch"
      lore:
        - "&7Contains a large amount of tokens"
        - "&8Right-click to open"
      chance: 5.0
      quantity: 1

    # XP Pouches
    small_xp_pouch:
      material: EMERALD
      displayName: "&aSmall XP Pouch"
      lore:
        - "&7Contains a small amount of experience"
        - "&8Right-click to open"
      chance: 50.0
      quantity: 1

    medium_xp_pouch:
      material: EMERALD
      displayName: "&2Medium XP Pouch"
      lore:
        - "&7Contains a moderate amount of experience"
        - "&8Right-click to open"
      chance: 25.0
      quantity: 1

    large_xp_pouch:
      material: EMERALD
      displayName: "&4Large XP Pouch"
      lore:
        - "&7Contains a large amount of experience"
        - "&8Right-click to open"
      chance: 5.0
      quantity: 1

    # Bundles
    xp_bundle:
      material: CHEST
      displayName: "&aXP Bundle"
      lore:
        - "&7A chest full of experience bottles"
        - "&7Right-click to open"
      chance: 5.0
      quantity: 1

    token_bundle:
      material: CHEST
      displayName: "&bToken Bundle"
      lore:
        - "&7A chest full of valuable tokens"
        - "&7Right-click to open"
      chance: 5.0
      quantity: 1

    coins_bundle:
      material: CHEST
      displayName: "&eCoins Bundle"
      lore:
        - "&7A chest full of coins"
        - "&7Right-click to open"
      chance: 5.0
      quantity: 1

  # Common Crate Configuration
  common:
    # XP Pouches
    small_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aSmall XP Pouch"
      lore:
        - "&7Contains a small amount of experience"
        - "&8Right-click to open"
      chance: 12.0
      quantity: 1

    medium_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aeMedium XP Pouch"
      lore:
        - "&7Contains a medium amount of experience"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    # Token Pouches
    small_token_pouch:
      material: EMERALD
      displayName: "&bSmall Token Pouch"
      lore:
        - "&7Contains a small amount of tokens"
        - "&8Right-click to open"
      chance: 12.0
      quantity: 1

    medium_token_pouch:
      material: EMERALD
      displayName: "&bMedium Token Pouch"
      lore:
        - "&7Contains a medium amount of tokens"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    # Gold Pouches
    small_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eSmall Gold Pouch"
      lore:
        - "&7Contains a small amount of gold"
        - "&8Right-click to open"
      chance: 12.0
      quantity: 1

    medium_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eMedium Gold Pouch"
      lore:
        - "&7Contains a medium amount of gold"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    # Bundles
    xp_bundle:
      material: CHEST
      displayName: "&aXP Bundle"
      lore:
        - "&7A chest full of experience bottles"
        - "&7Right-click to open"
      chance: 8.0
      quantity: 1

    coins_bundle:
      material: CHEST
      displayName: "&eCoins Bundle"
      lore:
        - "&7A chest full of coins"
        - "&7Right-click to open"
      chance: 8.0
      quantity: 1

    token_bundle:
      material: CHEST
      displayName: "&bToken Bundle"
      lore:
        - "&7A chest full of valuable tokens"
        - "&7Right-click to open"
      chance: 8.0
      quantity: 1

    # Keys
    rare_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &3&lRARE &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 8.0
      quantity: 1

    common_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &F&LCOMMON &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 8.0
      quantity: 1

  # Rare Crate Configuration
  rare:
    # XP Pouches
    large_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aLarge XP Pouch"
      lore:
        - "&7Contains a large amount of experience"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    medium_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aMedium XP Pouch"
      lore:
        - "&7Contains a medium amount of experience"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    small_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aSmall XP Pouch"
      lore:
        - "&7Contains a small amount of experience"
        - "&8Right-click to open"
      chance: 4.0
      quantity: 1

    # Token Pouches
    large_token_pouch:
      material: EMERALD
      displayName: "&bLarge Token Pouch"
      lore:
        - "&7Contains a large amount of tokens"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    medium_token_pouch:
      material: EMERALD
      displayName: "&bMedium Token Pouch"
      lore:
        - "&7Contains a medium amount of tokens"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    small_token_pouch:
      material: EMERALD
      displayName: "&bSmall Token Pouch"
      lore:
        - "&7Contains a small amount of tokens"
        - "&8Right-click to open"
      chance: 4.0
      quantity: 1

    # Gold Pouches
    large_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eLarge Gold Pouch"
      lore:
        - "&7Contains a large amount of gold"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    medium_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eMedium Gold Pouch"
      lore:
        - "&7Contains a medium amount of gold"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    small_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eSmall Gold Pouch"
      lore:
        - "&7Contains a small amount of gold"
        - "&8Right-click to open"
      chance: 4.0
      quantity: 1

    # Bundles
    coins_bundle:
      material: CHEST
      displayName: "&eCoins Bundle"
      lore:
        - "&7A chest full of coins"
        - "&7Right-click to open"
      chance: 8.0
      quantity: 1

    token_bundle:
      material: CHEST
      displayName: "&bToken Bundle"
      lore:
        - "&7A chest full of valuable tokens"
        - "&7Right-click to open"
      chance: 8.0
      quantity: 1

    xp_bundle:
      material: CHEST
      displayName: "&aXP Bundle"
      lore:
        - "&7A chest full of experience bottles"
        - "&7Right-click to open"
      chance: 8.0
      quantity: 1

    # Keys
    common_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &F&LCOMMON &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 6.0
      quantity: 1

    rare_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &3&lRARE &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 6.0
      quantity: 1

    uncommon_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &2&lUNCOMMON &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 6.0
      quantity: 1

    epic_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &5&lEPIC &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 6.0
      quantity: 1

    legendary_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &6&lLEGENDARY &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 4.0
      quantity: 1

  # Epic Crate Configuration
  epic:
    # XP Pouches
    small_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aSmall XP Pouch"
      lore:
        - "&7Contains a small amount of experience"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    medium_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aMedium XP Pouch"
      lore:
        - "&7Contains a medium amount of experience"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    large_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aLarge XP Pouch"
      lore:
        - "&7Contains a large amount of experience"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    # Token Pouches
    small_token_pouch:
      material: EMERALD
      displayName: "&bSmall Token Pouch"
      lore:
        - "&7Contains a small amount of tokens"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    medium_token_pouch:
      material: EMERALD
      displayName: "&bMedium Token Pouch"
      lore:
        - "&7Contains a medium amount of tokens"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    large_token_pouch:
      material: EMERALD
      displayName: "&bLarge Token Pouch"
      lore:
        - "&7Contains a large amount of tokens"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    # Gold Pouches
    small_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eSmall Gold Pouch"
      lore:
        - "&7Contains a small amount of gold"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    medium_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eMedium Gold Pouch"
      lore:
        - "&7Contains a medium amount of gold"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    large_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eLarge Gold Pouch"
      lore:
        - "&7Contains a large amount of gold"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    # Bundles
    xp_bundle:
      material: CHEST
      displayName: "&aXP Bundle"
      lore:
        - "&7A chest full of experience bottles"
        - "&7Right-click to open"
      chance: 6.0
      quantity: 1

    coins_bundle:
      material: CHEST
      displayName: "&eCoins Bundle"
      lore:
        - "&7A chest full of coins"
        - "&7Right-click to open"
      chance: 6.0
      quantity: 1

    token_bundle:
      material: CHEST
      displayName: "&bToken Bundle"
      lore:
        - "&7A chest full of valuable tokens"
        - "&7Right-click to open"
      chance: 6.0
      quantity: 1

    # Keys
    legendary_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &6&lLEGENDARY &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 5.0
      quantity: 1

    boss_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &4&lBOSS &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 4.0
      quantity: 1

    epic_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &5&lEPIC &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 5.0
      quantity: 1

    uncommon_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &2&lUNCOMMON &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 5.0
      quantity: 1

    common_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &F&LCOMMON &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 6.0
      quantity: 1

  # Legendary Crate Configuration
  legendary:
    # XP Pouches
    small_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aSmall XP Pouch"
      lore:
        - "&7Contains a small amount of experience"
        - "&8Right-click to open"
      chance: 12.0
      quantity: 1

    medium_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aMedium XP Pouch"
      lore:
        - "&7Contains a medium amount of experience"
        - "&8Right-click to open"
      chance: 12.0
      quantity: 1

    large_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aLarge XP Pouch"
      lore:
        - "&7Contains a large amount of experience"
        - "&8Right-click to open"
      chance: 12.0
      quantity: 1

    mega_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aMega XP Pouch"
      lore:
        - "&7Contains a mega amount of experience"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    # Token Pouches
    small_token_pouch:
      material: EMERALD
      displayName: "&bSmall Token Pouch"
      lore:
        - "&7Contains a small amount of tokens"
        - "&8Right-click to open"
      chance: 12.0
      quantity: 1

    medium_token_pouch:
      material: EMERALD
      displayName: "&bMedium Token Pouch"
      lore:
        - "&7Contains a medium amount of tokens"
        - "&8Right-click to open"
      chance: 12.0
      quantity: 1

    large_token_pouch:
      material: EMERALD
      displayName: "&bLarge Token Pouch"
      lore:
        - "&7Contains a large amount of tokens"
        - "&8Right-click to open"
      chance: 12.0
      quantity: 1

    mega_token_pouch:
      material: EMERALD
      displayName: "&bMega Token Pouch"
      lore:
        - "&7Contains a mega amount of tokens"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    # Gold Pouches
    small_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eSmall Gold Pouch"
      lore:
        - "&7Contains a small amount of gold"
        - "&8Right-click to open"
      chance: 12.0
      quantity: 1

    medium_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eMedium Gold Pouch"
      lore:
        - "&7Contains a medium amount of gold"
        - "&8Right-click to open"
      chance: 12.0
      quantity: 1

    large_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eLarge Gold Pouch"
      lore:
        - "&7Contains a large amount of gold"
        - "&8Right-click to open"
      chance: 12.0
      quantity: 1

    mega_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eMega Gold Pouch"
      lore:
        - "&7Contains a mega amount of gold"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    # Bundles
    xp_bundle:
      material: CHEST
      displayName: "&aXP Bundle"
      lore:
        - "&7A chest full of experience bottles"
        - "&7Right-click to open"
      chance: 8.0
      quantity: 1

    coins_bundle:
      material: CHEST
      displayName: "&eCoins Bundle"
      lore:
        - "&7A chest full of coins"
        - "&7Right-click to open"
      chance: 8.0
      quantity: 1

    token_bundle:
      material: CHEST
      displayName: "&bToken Bundle"
      lore:
        - "&7A chest full of valuable tokens"
        - "&7Right-click to open"
      chance: 8.0
      quantity: 1

  # Boss Crate Configuration
  boss:
    # Gold Pouches
    small_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eSmall Gold Pouch"
      lore:
        - "&7Contains a small amount of gold"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    medium_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eMedium Gold Pouch"
      lore:
        - "&7Contains a medium amount of gold"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    large_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eLarge Gold Pouch"
      lore:
        - "&7Contains a large amount of gold"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    mega_gold_pouch:
      material: GOLD_INGOT
      displayName: "&eMega Gold Pouch"
      lore:
        - "&7Contains a mega amount of gold"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    # Token Pouches
    small_token_pouch:
      material: EMERALD
      displayName: "&bSmall Token Pouch"
      lore:
        - "&7Contains a small amount of tokens"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    medium_token_pouch:
      material: EMERALD
      displayName: "&bMedium Token Pouch"
      lore:
        - "&7Contains a medium amount of tokens"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    large_token_pouch:
      material: EMERALD
      displayName: "&bLarge Token Pouch"
      lore:
        - "&7Contains a large amount of tokens"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    mega_token_pouch:
      material: EMERALD
      displayName: "&bMega Token Pouch"
      lore:
        - "&7Contains a mega amount of tokens"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    # XP Pouches
    small_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aSmall XP Pouch"
      lore:
        - "&7Contains a small amount of experience"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    medium_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aMedium XP Pouch"
      lore:
        - "&7Contains a medium amount of experience"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    large_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aLarge XP Pouch"
      lore:
        - "&7Contains a large amount of experience"
        - "&8Right-click to open"
      chance: 8.0
      quantity: 1

    mega_xp_pouch:
      material: EXPERIENCE_BOTTLE
      displayName: "&aMega XP Pouch"
      lore:
        - "&7Contains a mega amount of experience"
        - "&8Right-click to open"
      chance: 6.0
      quantity: 1

    # Bundles
    coins_bundle:
      material: CHEST
      displayName: "&eCoins Bundle"
      lore:
        - "&7A chest full of coins"
        - "&7Right-click to open"
      chance: 6.0
      quantity: 1

    token_bundle:
      material: CHEST
      displayName: "&bToken Bundle"
      lore:
        - "&7A chest full of valuable tokens"
        - "&7Right-click to open"
      chance: 6.0
      quantity: 1

    xp_bundle:
      material: CHEST
      displayName: "&aXP Bundle"
      lore:
        - "&7A chest full of experience bottles"
        - "&7Right-click to open"
      chance: 6.0
      quantity: 1

    # Keys
    boss_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &4&lBOSS &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 4.0
      quantity: 1

    common_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &F&LCOMMON &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 4.0
      quantity: 1

    legendary_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &6&lLEGENDARY &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 4.0
      quantity: 1

    rare_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &3&lRARE &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 4.0
      quantity: 1

    event_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &d&lEVENT &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 3.0
      quantity: 1

    epic_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &5&lEPIC &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 4.0
      quantity: 1

    uncommon_key:
      material: TRIPWIRE_HOOK
      displayName: "&8| &2&lUNCOMMON &8| &7&lKEY"
      lore:
        - "&8Coming in the future"
        - "&8Stay tuned for updates!"
      chance: 4.0
      quantity: 1

  # Event Crate Configuration
  event:
    # Bundles
    xp_bundle:
      material: CHEST
      displayName: "&aXP Bundle"
      lore:
        - "&7A chest full of experience bottles"
        - "&7Right-click to open"
      chance: 33.33
      quantity: 1

    token_bundle:
      material: CHEST
      displayName: "&bToken Bundle"
      lore:
        - "&7A chest full of valuable tokens"
        - "&7Right-click to open"
      chance: 33.33
      quantity: 1

    coins_bundle:
      material: CHEST
      displayName: "&eCoins Bundle"
      lore:
        - "&7A chest full of coins"
        - "&7Right-click to open"
      chance: 33.34
      quantity: 1

# Crate Keys Configuration
# These define the required keys for each crate type
keys:
  common:
    material: TRIPWIRE_HOOK
    displayName: "&8| &F&LCOMMON &8| &7&lKEY"
    lore:
      - "&8Coming in the future"
      - "&8Stay tuned for updates!"

  uncommon:
    material: TRIPWIRE_HOOK
    displayName: "&8| &2&lUNCOMMON &8| &7&lKEY"
    lore:
      - "&8Coming in the future"
      - "&8Stay tuned for updates!"

  rare:
    material: TRIPWIRE_HOOK
    displayName: "&8| &3&lRARE &8| &7&lKEY"
    lore:
      - "&8Coming in the future"
      - "&8Stay tuned for updates!"

  epic:
    material: TRIPWIRE_HOOK
    displayName: "&8| &5&lEPIC &8| &7&lKEY"
    lore:
      - "&8Coming in the future"
      - "&8Stay tuned for updates!"

  legendary:
    material: TRIPWIRE_HOOK
    displayName: "&8| &6&lLEGENDARY &8| &7&lKEY"
    lore:
      - "&8Coming in the future"
      - "&8Stay tuned for updates!"

  boss:
    material: TRIPWIRE_HOOK
    displayName: "&8| &4&lBOSS &8| &7&lKEY"
    lore:
      - "&8Coming in the future"
      - "&8Stay tuned for updates!"

  event:
    material: TRIPWIRE_HOOK
    displayName: "&8| &d&lEVENT &8| &7&lKEY"
    lore:
      - "&8Coming in the future"
      - "&8Stay tuned for updates!"

# Crate locations will be stored here automatically
locations: {}
