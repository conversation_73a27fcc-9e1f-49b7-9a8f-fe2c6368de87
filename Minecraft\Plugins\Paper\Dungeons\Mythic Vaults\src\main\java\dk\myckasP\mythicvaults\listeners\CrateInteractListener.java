package dk.myckasP.mythicvaults.listeners;

import dk.myckasP.mythicvaults.MythicVaults;
import dk.myckasP.mythicvaults.gui.CrateGUI;
import dk.myckasP.mythicvaults.gui.PreviewGUI;
import dk.myckasP.mythicvaults.models.Crate;
import org.bukkit.ChatColor;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.PlayerInteractEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class CrateInteractListener implements Listener {
    private final MythicVaults plugin;
    private final Map<UUID, CrateGUI> activeCrateGUIs;

    public CrateInteractListener(MythicVaults plugin) {
        this.plugin = plugin;
        this.activeCrateGUIs = new HashMap<>();
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getClickedBlock() == null) {
            return;
        }

        Player player = event.getPlayer();
        Crate crate = plugin.getCrateManager().getCrate(event.getClickedBlock().getLocation());

        if (crate == null) {
            return;
        }

        event.setCancelled(true);

        if (!player.hasPermission("mythicvaults.use")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to use crates!");
            return;
        }

        if (event.getAction() == Action.LEFT_CLICK_BLOCK) {
            // Check for shift+left-click to delete crate
            if (player.isSneaking()) {
                // Delete crate functionality
                if (player.hasPermission("mythicvaults.delete")) {
                    plugin.getCrateManager().removeCrate(event.getClickedBlock().getLocation());
                    player.sendMessage(ChatColor.RED + "Deleted " + crate.getType().getDisplayName() + ChatColor.RED + " at this location!");
                    player.playSound(player.getLocation(), Sound.BLOCK_ANVIL_BREAK, 1.0f, 1.0f);
                } else {
                    player.sendMessage(ChatColor.RED + "You don't have permission to delete crates!");
                }
            } else {
                // Preview crate contents
                if (player.hasPermission("mythicvaults.preview")) {
                    PreviewGUI previewGUI = new PreviewGUI(plugin, player, crate);
                    previewGUI.open();
                    player.sendMessage(ChatColor.YELLOW + "Previewing " + crate.getType().getDisplayName() + ChatColor.YELLOW + " contents...");
                } else {
                    player.sendMessage(ChatColor.RED + "You don't have permission to preview crates!");
                }
            }
        } else if (event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            // Open crate - check for key requirement
            if (activeCrateGUIs.containsKey(player.getUniqueId())) {
                player.sendMessage(ChatColor.RED + "You already have a crate open!");
                return;
            }

            // Check if player has at least 3 empty inventory slots
            if (getEmptyInventorySlots(player) < 3) {
                player.sendMessage(ChatColor.RED + "Your inventory is full! You need at least 3 empty slots to open a crate.");

                // Gently push the player backward as visual feedback
                pushPlayerBackward(player);
                return;
            }

            // Check if player has the required key in their main hand
            if (!plugin.getKeyUtils().hasKey(player, crate.getType())) {
                String keyName = plugin.getKeyUtils().getKeyName(crate.getType());
                player.sendMessage(ChatColor.RED + "You need to a " + keyName + ChatColor.RED + " to open this crate!");

                // Gently push the player backward as visual feedback
                pushPlayerBackward(player);
                return;
            }

            // Consume the key
            if (!plugin.getKeyUtils().consumeKey(player, crate.getType())) {
                player.sendMessage(ChatColor.RED + "Failed to consume key! Please try again.");
                return;
            }

            // Play key usage sound
            player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_CHIME, 1.0f, 1.2f);

            // Start visual effects for crate opening
            plugin.getVisualEffectsManager().startCrateOpeningEffects(player, crate.getLocation(), crate.getType());

            CrateGUI crateGUI = new CrateGUI(plugin, player, crate);
            activeCrateGUIs.put(player.getUniqueId(), crateGUI);
            crateGUI.open();

            String keyName = plugin.getKeyUtils().getKeyName(crate.getType());
            player.sendMessage(ChatColor.GREEN + "Used " + keyName + ChatColor.GREEN + "! Opening " + crate.getType().getDisplayName() + ChatColor.GREEN + "!");
            player.sendMessage(ChatColor.YELLOW + "Click on the white glass panes to reveal rewards!");
        }
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();

        // Handle CrateGUI clicks
        if (event.getInventory().getHolder() instanceof CrateGUI) {
            event.setCancelled(true);
            CrateGUI crateGUI = (CrateGUI) event.getInventory().getHolder();
            
            if (crateGUI.getPlayer().equals(player)) {
                crateGUI.handleClick(event.getSlot());
            }
            return;
        }

        // Handle PreviewGUI clicks
        if (event.getInventory().getHolder() instanceof PreviewGUI) {
            event.setCancelled(true);
            player.closeInventory();
            return;
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getPlayer();

        // Handle CrateGUI close
        if (event.getInventory().getHolder() instanceof CrateGUI) {
            CrateGUI crateGUI = activeCrateGUIs.get(player.getUniqueId());
            if (crateGUI != null) {
                // Prevent closing until the entire crate opening process is complete
                if (!crateGUI.canClose()) {
                    String message = null;
                    if (crateGUI.isAnimationsActive()) {
                        message = ChatColor.YELLOW + "Please wait for the animation to finish!";
                    } else if (crateGUI.areAllRewardsRevealed()) {
                        // All rewards revealed, waiting for auto-close - prevent manual closing
                        // Don't show any message, just silently prevent closing
                    } else {
                        message = ChatColor.YELLOW + "You must click on the white glass panes to reveal your rewards!";
                    }

                    if (message != null) {
                        player.sendMessage(message);
                    }

                    // Reopen the inventory after a short delay
                    plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                        if (activeCrateGUIs.containsKey(player.getUniqueId())) {
                            player.openInventory(crateGUI.getInventory());
                        }
                    }, 1L);
                    return;
                }

                // Stop visual effects when crate is closed
                plugin.getVisualEffectsManager().stopEffects(player);

                // Remove and clean up the GUI
                activeCrateGUIs.remove(player.getUniqueId());
                crateGUI.close();
            }
        }
    }

    /**
     * Manually clean up a crate GUI for a player (used by auto-close timer)
     */
    public void cleanupCrateGUI(Player player) {
        CrateGUI crateGUI = activeCrateGUIs.remove(player.getUniqueId());
        if (crateGUI != null) {
            // Stop visual effects when crate is closed
            plugin.getVisualEffectsManager().stopEffects(player);
            crateGUI.close();
        }
    }

    /**
     * Counts the number of empty slots in a player's inventory
     * @param player The player to check
     * @return Number of empty inventory slots
     */
    private int getEmptyInventorySlots(Player player) {
        int emptySlots = 0;
        for (int i = 0; i < 36; i++) { // Main inventory slots (0-35)
            if (player.getInventory().getItem(i) == null) {
                emptySlots++;
            }
        }
        return emptySlots;
    }

    /**
     * Gently pushes a player backward as visual feedback
     */
    private void pushPlayerBackward(Player player) {
        // Get the direction the player is facing and reverse it
        org.bukkit.util.Vector direction = player.getLocation().getDirection().normalize();
        direction.multiply(-0.3); // Gentle backward push - slightly stronger than original
        direction.setY(0.1); // Small upward component to make it feel natural

        // Apply the velocity
        player.setVelocity(direction);

        // Play a subtle sound effect
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_ITEM_BREAK, 0.5f, 0.8f);

        // Add particle effect for visual feedback
        player.spawnParticle(org.bukkit.Particle.CLOUD, player.getLocation().add(0, 1, 0), 5, 0.3, 0.3, 0.3, 0.1);
    }
}
