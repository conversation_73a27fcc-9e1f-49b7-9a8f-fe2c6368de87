# Mythic Vaults Configuration File
# Advanced crate system for dungeon servers

# Plugin Settings
settings:
  # Show version message to operators on join
  show-version-on-join: true
  
  # Debug mode (shows additional information in console)
  debug: false

# Messages
messages:
  # General messages
  no-permission: "&cYou don't have permission to use this!"
  player-only: "&cThis command can only be used by players!"
  crate-opened: "&a<PERSON><PERSON> opened a %crate_type%!"
  rewards-received: "&aY<PERSON> received %count% rewards!"
  
  # Crate interaction messages
  crate-preview: "&eLeft-click to preview contents, right-click to open!"
  crate-opening: "&aOpening %crate_type%! Click on white glass panes to reveal rewards!"
  clicks-remaining: "&eClicks remaining: %clicks%"
  
  # Admin messages
  crate-set: "&aSuccessfully set %crate_type% at %location%!"
  crate-removed: "&aCrate removed from %location%!"
  config-reloaded: "&aConfiguration reloaded successfully!"
  
  # Error messages
  invalid-crate-type: "&cInvalid crate type! Available types: %types%"
  no-target-block: "&cYou must be looking at a block!"
  crate-already-open: "&cYou already have a crate open!"

# Sound Settings
sounds:
  # Sound when clicking on white glass panes
  click-sound:
    sound: ENTITY_EXPERIENCE_ORB_PICKUP
    volume: 1.0
    pitch: 1.0
  
  # Sound when winning an item
  win-sound:
    sound: ENTITY_PLAYER_LEVELUP
    volume: 1.0
    pitch: 1.0
  
  # Sound when opening crate GUI
  open-sound:
    sound: BLOCK_CHEST_OPEN
    volume: 0.8
    pitch: 1.0

# Animation Settings
animation:
  # Duration of scrolling animation in ticks (20 ticks = 1 second)
  scroll-duration: 60

  # Speed of scrolling animation in ticks between item changes
  scroll-speed: 2

  # Delay before giving rewards in ticks
  reward-delay: 20

  # Delay before closing inventory in ticks
  close-delay: 60

# Hologram Settings
holograms:
  # Enable or disable hologram display above crates
  enabled: true

  # Height offset above the crate block (in blocks)
  height-offset: 1.5

  # Text to display in the hologram (deprecated - now uses multi-line format)
  # Available placeholders: {crate_name}, {crate_type}
  text: "{crate_name}"

  # View range for holograms (in blocks)
  view-range: 32.0

  # Whether to show holograms through blocks
  see-through: false

  # Whether to add shadow to hologram text
  shadowed: true

  # Scale factor for hologram text (1.0 = normal size)
  scale: 1.2

  # Enable/disable holograms for specific crate types
  enabled-types:
    common: true
    uncommon: true
    rare: true
    epic: true
    legendary: true
    boss: true
    event: true
