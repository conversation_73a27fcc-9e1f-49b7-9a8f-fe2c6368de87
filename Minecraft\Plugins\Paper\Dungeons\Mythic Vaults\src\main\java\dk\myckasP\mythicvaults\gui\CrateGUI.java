package dk.myckasP.mythicvaults.gui;

import dk.myckasP.mythicvaults.MythicVaults;
import dk.myckasP.mythicvaults.models.Crate;
import dk.myckasP.mythicvaults.models.CrateReward;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;

public class CrateGUI implements InventoryHolder {
    private final MythicVaults plugin;
    private final Player player;
    private final Crate crate;
    private final Inventory inventory;
    private final Set<Integer> clickableSlots;
    private final Map<Integer, BukkitRunnable> scrollingTasks;
    private final Set<Integer> selectedSlots;
    private final List<ItemStack> rewards;
    private int clicksRemaining;
    private boolean animationsActive;
    private boolean crateOpeningComplete;
    private boolean rewardsGiven;

    // GUI Layout constants - Minimized gray glass, maximized white clickable areas
    private static final int[] GRAY_GLASS_SLOTS = {0, 8, 9, 17, 18, 26};
    private static final int[] WHITE_GLASS_SLOTS = {1, 2, 3, 4, 5, 6, 7, 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25};

    public CrateGUI(MythicVaults plugin, Player player, Crate crate) {
        this.plugin = plugin;
        this.player = player;
        this.crate = crate;
        this.inventory = Bukkit.createInventory(this, 27, crate.getType().getDisplayName());
        this.clickableSlots = new HashSet<>();
        this.scrollingTasks = new HashMap<>();
        this.selectedSlots = new HashSet<>();
        this.rewards = new ArrayList<>();
        this.clicksRemaining = 3;
        this.animationsActive = false;
        this.crateOpeningComplete = false;
        this.rewardsGiven = false;

        setupGUI();
        for (int slot : WHITE_GLASS_SLOTS) {
            clickableSlots.add(slot);
        }
    }

    private void setupGUI() {
        // Fill with gray glass panes on the sides
        ItemStack grayGlass = createGlassPane(Material.GRAY_STAINED_GLASS_PANE, ChatColor.GRAY + "");
        for (int slot : GRAY_GLASS_SLOTS) {
            inventory.setItem(slot, grayGlass);
        }

        // Fill with white glass panes in the middle
        ItemStack whiteGlass = createGlassPane(Material.WHITE_STAINED_GLASS_PANE, 
            ChatColor.WHITE + "Click to reveal reward! (" + clicksRemaining + " clicks remaining)");
        for (int slot : WHITE_GLASS_SLOTS) {
            inventory.setItem(slot, whiteGlass);
        }
    }

    private ItemStack createGlassPane(Material material, String name) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(name);
            item.setItemMeta(meta);
        }
        return item;
    }

    public void handleClick(int slot) {
        if (!clickableSlots.contains(slot) || selectedSlots.contains(slot) || clicksRemaining <= 0) {
            return;
        }

        // Play XP sound
        player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);

        selectedSlots.add(slot);
        clicksRemaining--;
        animationsActive = true;

        // Start rolling effects at the crate location
        plugin.getVisualEffectsManager().createRollingEffects(player, crate.getLocation(), crate.getType());

        // Start scrolling animation
        startScrollingAnimation(slot);

        // Update remaining white glass panes
        updateRemainingSlots();
    }

    private void startScrollingAnimation(int slot) {
        List<ItemStack> crateRewards;
        if (crate.getType().name().equals("UNCOMMON")) {
            crateRewards = createQuantityDisplayItems();
        } else {
            crateRewards = createFilteredDisplayItems();
        }
        if (crateRewards.isEmpty()) {
            return;
        }

        BukkitRunnable scrollTask = new BukkitRunnable() {
            private int ticks = 0;
            private final int maxTicks = 60; // 3 seconds at 20 ticks per second
            private final Random random = new Random();

            @Override
            public void run() {
                if (ticks >= maxTicks) {
                    // Animation finished, show final reward with percentage
                    CrateReward finalReward = crate.getRandomCrateRewards(1).get(0);
                    ItemStack rewardItem = createWonItem(finalReward);
                    rewards.add(finalReward.getItem()); // Add the base item for giving to player
                    inventory.setItem(slot, rewardItem); // Display the item with percentage info

                    // Play level up sound
                    player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);

                    // Create individual reward effect
                    plugin.getVisualEffectsManager().createIndividualRewardEffect(player, crate.getLocation(), crate.getType());

                    // Check if all clicks are used
                    if (clicksRemaining <= 0 && !rewardsGiven) {
                        animationsActive = false;
                        rewardsGiven = true;

                        // Create celebration effects when all rewards are revealed
                        plugin.getVisualEffectsManager().createRewardCelebrationEffects(
                            player, crate.getLocation(), crate.getType(), rewards.size()
                        );

                        finishCrateOpening();
                    }

                    scrollingTasks.remove(slot);
                    cancel();
                    return;
                }

                // Show random item during animation
                ItemStack randomItem = crateRewards.get(random.nextInt(crateRewards.size()));
                inventory.setItem(slot, randomItem);

                // Play rolling sound effect every few ticks
                if (ticks % 4 == 0) { // Play sound every 4 ticks (0.2 seconds)
                    float pitch = 0.8f + (ticks / (float) maxTicks) * 0.8f; // Gradually increase pitch
                    player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_PLING, 0.3f, pitch);
                }

                ticks++;
            }
        };

        scrollingTasks.put(slot, scrollTask);
        scrollTask.runTaskTimer(plugin, 0L, 2L); // Run every 2 ticks (0.1 seconds)
    }

    private void updateRemainingSlots() {
        ItemStack whiteGlass = createGlassPane(Material.WHITE_STAINED_GLASS_PANE, 
            ChatColor.WHITE + "Click to reveal reward! (" + clicksRemaining + " clicks remaining)");
        
        for (int slot : WHITE_GLASS_SLOTS) {
            if (!selectedSlots.contains(slot)) {
                inventory.setItem(slot, whiteGlass);
            }
        }
    }

    private void finishCrateOpening() {
        // Give rewards to player after a short delay
        new BukkitRunnable() {
            @Override
            public void run() {
                for (ItemStack reward : rewards) {
                    // Add to inventory or drop if full
                    HashMap<Integer, ItemStack> leftover = player.getInventory().addItem(reward);
                    for (ItemStack item : leftover.values()) {
                        player.getWorld().dropItemNaturally(player.getLocation(), item);
                    }
                }
                
                player.sendMessage(ChatColor.GREEN + "You received " + rewards.size() + " rewards from the " + 
                    crate.getType().getDisplayName() + ChatColor.GREEN + "!");
                
                // Mark crate opening as complete and close inventory after 3 seconds
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        crateOpeningComplete = true;
                        // Clean up the GUI first, then close inventory
                        plugin.getCrateInteractListener().cleanupCrateGUI(player);
                        player.closeInventory();
                    }
                }.runTaskLater(plugin, 60L);
            }
        }.runTaskLater(plugin, 20L); // 1 second delay
    }

    public void open() {
        player.openInventory(inventory);
    }

    public void close() {
        // Stop visual effects when GUI is closed
        plugin.getVisualEffectsManager().stopEffects(player);

        // Cancel all running animations
        for (BukkitRunnable task : scrollingTasks.values()) {
            if (task != null && !task.isCancelled()) {
                task.cancel();
            }
        }
        scrollingTasks.clear();
    }

    @Override
    public Inventory getInventory() {
        return inventory;
    }

    public Player getPlayer() {
        return player;
    }

    public Crate getCrate() {
        return crate;
    }

    public boolean isAnimationsActive() {
        return animationsActive;
    }

    public boolean isCrateOpeningComplete() {
        return crateOpeningComplete;
    }

    public boolean canClose() {
        return crateOpeningComplete;
    }

    public boolean areAllRewardsRevealed() {
        return clicksRemaining <= 0;
    }

    /**
     * Creates display items with proper quantities for the rolling animation
     */
    private List<ItemStack> createQuantityDisplayItems() {
        List<ItemStack> displayItems = new ArrayList<>();
        List<CrateReward> crateRewards = crate.getRewards();

        for (CrateReward reward : crateRewards) {
            ItemStack displayItem = reward.getDisplayItem().clone();
            displayItem.setAmount(1); // Always display as quantity 1

            // Filter out "Right-click to open" text from rolling animation items
            ItemMeta meta = displayItem.getItemMeta();
            if (meta != null && meta.getLore() != null) {
                List<String> filteredLore = filterRightClickText(meta.getLore());
                meta.setLore(filteredLore);
                displayItem.setItemMeta(meta);
            }

            displayItems.add(displayItem);
        }

        return displayItems;
    }

    /**
     * Creates filtered display items for non-uncommon crates (removes right-click text)
     */
    private List<ItemStack> createFilteredDisplayItems() {
        List<ItemStack> displayItems = new ArrayList<>();
        List<ItemStack> originalItems = crate.getAllRewardItems();

        for (ItemStack originalItem : originalItems) {
            ItemStack displayItem = originalItem.clone();

            // Filter out "Right-click to open" text from rolling animation items
            ItemMeta meta = displayItem.getItemMeta();
            if (meta != null && meta.getLore() != null) {
                List<String> filteredLore = filterRightClickText(meta.getLore());
                meta.setLore(filteredLore);
                displayItem.setItemMeta(meta);
            }

            displayItems.add(displayItem);
        }

        return displayItems;
    }

    /**
     * Creates a won item with percentage information added to the lore
     * @param reward The crate reward that was won
     * @return ItemStack with percentage information in lore
     */
    private ItemStack createWonItem(CrateReward reward) {
        ItemStack item = reward.getDisplayItem().clone();
        ItemMeta meta = item.getItemMeta();

        if (meta != null) {
            List<String> lore = meta.getLore();
            if (lore == null) {
                lore = new ArrayList<>();
            }

            // Filter out "Right-click to open" text from lore
            lore = filterRightClickText(lore);

            // Add percentage information to the lore
            lore.add("");
            lore.add(ChatColor.GREEN + "✓ " + ChatColor.GOLD + "Won! (" + formatPercentage(reward.getChance()) + "% chance)");

            meta.setLore(lore);
            item.setItemMeta(meta);
        }

        return item;
    }

    /**
     * Filters out "Right-click to open" text from item lore
     * @param lore The original lore list
     * @return Filtered lore list without right-click text
     */
    private List<String> filterRightClickText(List<String> lore) {
        List<String> filteredLore = new ArrayList<>();
        for (String line : lore) {
            // Remove color codes for comparison
            String plainLine = ChatColor.stripColor(line).toLowerCase();
            if (!plainLine.contains("right-click to open") && !plainLine.contains("right click to open")) {
                filteredLore.add(line);
            }
        }
        return filteredLore;
    }

    /**
     * Formats the percentage to show appropriate decimal places
     * @param chance The chance value to format
     * @return Formatted percentage string
     */
    private String formatPercentage(double chance) {
        // If it's a whole number, show no decimals
        if (chance == Math.floor(chance)) {
            return String.format("%.0f", chance);
        }
        // If it has one decimal place, show one decimal
        else if (chance * 10 == Math.floor(chance * 10)) {
            return String.format("%.1f", chance);
        }
        // Otherwise show two decimal places
        else {
            return String.format("%.2f", chance);
        }
    }
}
