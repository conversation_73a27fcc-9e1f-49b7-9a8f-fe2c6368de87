package dk.myckasP.mythicvaults.commands;

import dk.myckasP.mythicvaults.MythicVaults;
import dk.myckasP.mythicvaults.enums.CrateType;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class GiveKeyCommand implements CommandExecutor, TabCompleter {
    private final MythicVaults plugin;

    public GiveKeyCommand(MythicVaults plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("mythicvaults.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command!");
            return true;
        }

        if (args.length < 2 || args.length > 3) {
            sender.sendMessage(ChatColor.RED + "Usage: /givekey <player> <cratetype> [amount]");
            sender.sendMessage(ChatColor.GRAY + "Available types: " + Arrays.toString(CrateType.values()));
            return true;
        }

        Player targetPlayer = Bukkit.getPlayer(args[0]);
        if (targetPlayer == null) {
            sender.sendMessage(ChatColor.RED + "Player '" + args[0] + "' not found!");
            return true;
        }

        CrateType crateType = CrateType.fromString(args[1]);
        if (crateType == null) {
            sender.sendMessage(ChatColor.RED + "Invalid crate type! Available types:");
            for (CrateType type : CrateType.values()) {
                sender.sendMessage(ChatColor.GRAY + "- " + type.name().toLowerCase());
            }
            return true;
        }

        int amount = 1;
        if (args.length == 3) {
            try {
                amount = Integer.parseInt(args[2]);
                if (amount <= 0) {
                    sender.sendMessage(ChatColor.RED + "Amount must be a positive number!");
                    return true;
                }
            } catch (NumberFormatException e) {
                sender.sendMessage(ChatColor.RED + "Invalid amount! Please enter a valid number.");
                return true;
            }
        }

        // Create and give the key
        ItemStack key = plugin.getKeyUtils().createKey(crateType);
        key.setAmount(amount);
        
        // Add to inventory or drop if full
        targetPlayer.getInventory().addItem(key).values().forEach(leftover -> 
            targetPlayer.getWorld().dropItemNaturally(targetPlayer.getLocation(), leftover));

        String keyName = plugin.getKeyUtils().getKeyName(crateType);
        sender.sendMessage(ChatColor.GREEN + "Gave " + amount + "x " + keyName + ChatColor.GREEN + " to " + targetPlayer.getName());
        targetPlayer.sendMessage(ChatColor.GREEN + "You received " + amount + "x " + keyName + ChatColor.GREEN + "!");

        return true;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!sender.hasPermission("mythicvaults.admin")) {
            return new ArrayList<>();
        }

        if (args.length == 1) {
            // Player names
            List<String> playerNames = new ArrayList<>();
            String input = args[0].toLowerCase();
            for (Player player : Bukkit.getOnlinePlayers()) {
                if (player.getName().toLowerCase().startsWith(input)) {
                    playerNames.add(player.getName());
                }
            }
            return playerNames;
        } else if (args.length == 2) {
            // Crate types
            List<String> completions = new ArrayList<>();
            String input = args[1].toLowerCase();
            for (CrateType type : CrateType.values()) {
                if (type.name().toLowerCase().startsWith(input)) {
                    completions.add(type.name().toLowerCase());
                }
            }
            return completions;
        } else if (args.length == 3) {
            // Amount suggestions
            return Arrays.asList("1", "5", "10");
        }

        return new ArrayList<>();
    }
}
