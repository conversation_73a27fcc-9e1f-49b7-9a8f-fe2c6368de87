package dk.myckasP.mythicvaults;

import dk.myckasP.mythicvaults.commands.GiveKeyCommand;
import dk.myckasP.mythicvaults.commands.MythicVaultsCommand;
import dk.myckasP.mythicvaults.commands.SetCrateCommand;
import dk.myckasP.mythicvaults.listeners.CrateInteractListener;
import dk.myckasP.mythicvaults.listeners.HologramListener;
import dk.myckasP.mythicvaults.listeners.PlayerJoinListener;
import dk.myckasP.mythicvaults.managers.CrateManager;
import dk.myckasP.mythicvaults.managers.ConfigManager;
import dk.myckasP.mythicvaults.managers.HologramManager;
import dk.myckasP.mythicvaults.managers.VisualEffectsManager;
import dk.myckasP.mythicvaults.utils.KeyUtils;
import org.bukkit.plugin.java.JavaPlugin;

public final class MythicVaults extends JavaPlugin {

    private static MythicVaults instance;
    private CrateManager crateManager;
    private ConfigManager configManager;
    private HologramManager hologramManager;
    private VisualEffectsManager visualEffectsManager;
    private KeyUtils keyUtils;
    private CrateInteractListener crateInteractListener;

    @Override
    public void onEnable() {
        instance = this;
        
        // Initialize managers
        this.configManager = new ConfigManager(this);
        this.hologramManager = new HologramManager(this);
        this.visualEffectsManager = new VisualEffectsManager(this);
        this.crateManager = new CrateManager(this);
        this.keyUtils = new KeyUtils(this);
        
        // Register commands
        getCommand("mythicvaults").setExecutor(new MythicVaultsCommand(this));
        getCommand("setcrate").setExecutor(new SetCrateCommand(this));
        getCommand("givekey").setExecutor(new GiveKeyCommand(this));
        
        // Register listeners
        this.crateInteractListener = new CrateInteractListener(this);
        getServer().getPluginManager().registerEvents(crateInteractListener, this);
        getServer().getPluginManager().registerEvents(new PlayerJoinListener(this), this);
        getServer().getPluginManager().registerEvents(new HologramListener(this), this);
        
        // Load configuration
        configManager.loadConfig();
        crateManager.loadCrates();

        // Start periodic hologram cleanup task (every 5 minutes)
        getServer().getScheduler().runTaskTimer(this, () -> {
            hologramManager.cleanupInvalidHolograms();
        }, 6000L, 6000L); // 6000 ticks = 5 minutes

        getLogger().info("Mythic Vaults v" + getDescription().getVersion() + " has been enabled by MyckasP!");
    }

    @Override
    public void onDisable() {
        if (visualEffectsManager != null) {
            visualEffectsManager.stopAllEffects();
        }
        if (hologramManager != null) {
            hologramManager.removeAllHolograms();
        }
        if (crateManager != null) {
            crateManager.saveCrates();
        }
        getLogger().info("Mythic Vaults has been disabled!");
    }

    public static MythicVaults getInstance() {
        return instance;
    }

    public CrateManager getCrateManager() {
        return crateManager;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public HologramManager getHologramManager() {
        return hologramManager;
    }

    public VisualEffectsManager getVisualEffectsManager() {
        return visualEffectsManager;
    }

    public KeyUtils getKeyUtils() {
        return keyUtils;
    }

    public CrateInteractListener getCrateInteractListener() {
        return crateInteractListener;
    }
}
