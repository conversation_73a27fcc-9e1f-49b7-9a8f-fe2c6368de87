package dk.myckasP.mythicvaults.utils;

import dk.myckasP.mythicvaults.MythicVaults;
import dk.myckasP.mythicvaults.enums.CrateType;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

public class KeyUtils {
    private final MythicVaults plugin;

    public KeyUtils(MythicVaults plugin) {
        this.plugin = plugin;
    }

    /**
     * Creates a crate key ItemStack for the specified crate type
     */
    public ItemStack createKey(CrateType crateType) {
        ConfigurationSection keySection = plugin.getCrateManager().getCratesConfig()
                .getConfigurationSection("keys." + crateType.getConfigKey());
        
        if (keySection == null) {
            // Fallback key if not configured
            return createFallbackKey(crateType);
        }

        Material material = Material.valueOf(keySection.getString("material", "STONE"));
        String displayName = ChatColor.translateAlternateColorCodes('&', 
            keySection.getString("displayName", crateType.getDisplayName() + " Key"));
        List<String> lore = keySection.getStringList("lore");
        
        ItemStack key = new ItemStack(material);
        ItemMeta meta = key.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(displayName);
            
            // Translate color codes in lore
            List<String> coloredLore = new ArrayList<>();
            for (String line : lore) {
                coloredLore.add(ChatColor.translateAlternateColorCodes('&', line));
            }
            meta.setLore(coloredLore);
            
            key.setItemMeta(meta);
        }
        
        return key;
    }

    /**
     * Creates a fallback key if configuration is missing
     */
    private ItemStack createFallbackKey(CrateType crateType) {
        ItemStack key = new ItemStack(Material.TRIPWIRE_HOOK);
        ItemMeta meta = key.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(crateType.getDisplayName() + ChatColor.RESET + " Key");
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "A key for " + crateType.getPlainDisplayName().toLowerCase() + " crates");
            lore.add(ChatColor.DARK_GRAY + "Right-click a " + crateType.getPlainDisplayName() + " to use");
            meta.setLore(lore);
            key.setItemMeta(meta);
        }
        return key;
    }

    /**
     * Checks if a player has the required key for a crate type in their main hand
     */
    public boolean hasKey(Player player, CrateType crateType) {
        ItemStack requiredKey = createKey(crateType);
        ItemStack heldItem = player.getInventory().getItemInMainHand();

        return heldItem != null && isSameKey(heldItem, requiredKey);
    }

    /**
     * Consumes one key from the player's main hand
     */
    public boolean consumeKey(Player player, CrateType crateType) {
        ItemStack requiredKey = createKey(crateType);
        ItemStack heldItem = player.getInventory().getItemInMainHand();

        if (heldItem != null && isSameKey(heldItem, requiredKey)) {
            if (heldItem.getAmount() > 1) {
                heldItem.setAmount(heldItem.getAmount() - 1);
            } else {
                player.getInventory().setItemInMainHand(null);
            }
            return true;
        }

        return false;
    }

    /**
     * Checks if two ItemStacks represent the same key
     */
    private boolean isSameKey(ItemStack item1, ItemStack item2) {
        if (item1 == null || item2 == null) {
            return false;
        }
        
        if (item1.getType() != item2.getType()) {
            return false;
        }
        
        ItemMeta meta1 = item1.getItemMeta();
        ItemMeta meta2 = item2.getItemMeta();
        
        if (meta1 == null || meta2 == null) {
            return meta1 == meta2;
        }
        
        // Compare display names
        String name1 = meta1.getDisplayName();
        String name2 = meta2.getDisplayName();
        
        if (name1 == null || name2 == null) {
            return name1 == null && name2 == null;
        }
        
        return name1.equals(name2);
    }

    /**
     * Gets the required key name for a crate type
     */
    public String getKeyName(CrateType crateType) {
        ItemStack key = createKey(crateType);
        ItemMeta meta = key.getItemMeta();
        if (meta != null && meta.getDisplayName() != null) {
            return meta.getDisplayName();
        }
        return crateType.getDisplayName() + " Key";
    }
}
