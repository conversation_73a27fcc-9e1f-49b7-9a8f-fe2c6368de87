package dk.myckasP.mythicvaults.listeners;

import dk.myckasP.mythicvaults.MythicVaults;
import dk.myckasP.mythicvaults.models.Crate;
import org.bukkit.Chunk;
import org.bukkit.Location;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.world.ChunkLoadEvent;
import org.bukkit.event.world.ChunkUnloadEvent;

public class HologramListener implements Listener {
    private final MythicVaults plugin;
    
    public HologramListener(MythicVaults plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onChunkLoad(ChunkLoadEvent event) {
        Chunk chunk = event.getChunk();

        // Only process if this is a new chunk load (not during server startup)
        // and only if holograms are enabled
        if (!plugin.getHologramManager().isHologramEnabled()) {
            return;
        }

        // Check if any crates are in this chunk and recreate their holograms if missing
        for (Crate crate : plugin.getCrateManager().getAllCrates()) {
            Location crateLocation = crate.getLocation();

            // Check if the crate is in the loaded chunk
            if (crateLocation.getChunk().equals(chunk)) {
                // Only recreate if hologram doesn't exist or is invalid
                if (!plugin.getHologramManager().hasHologram(crateLocation)) {
                    plugin.getHologramManager().createHologram(crate);
                    plugin.getLogger().info("Recreated missing hologram for crate at " +
                        crateLocation.getBlockX() + ", " + crateLocation.getBlockY() + ", " + crateLocation.getBlockZ() +
                        " due to chunk load");
                }
            }
        }
    }
    
    @EventHandler
    public void onChunkUnload(ChunkUnloadEvent event) {
        Chunk chunk = event.getChunk();

        // Clean up our references for crates in the unloading chunk to prevent memory leaks
        // Note: TextDisplay entities with setPersistent(true) should survive chunk unloads,
        // so we only clean up our internal references, not the actual entities
        for (Crate crate : plugin.getCrateManager().getAllCrates()) {
            Location crateLocation = crate.getLocation();

            // Check if the crate is in the unloading chunk
            if (crateLocation.getChunk().equals(chunk)) {
                // Only clean up our reference, don't remove the persistent entity
                plugin.getHologramManager().cleanupHologramReference(crateLocation);
                plugin.getLogger().fine("Cleaned up hologram reference for crate at " +
                    crateLocation.getBlockX() + ", " + crateLocation.getBlockY() + ", " + crateLocation.getBlockZ() +
                    " due to chunk unload");
            }
        }
    }
}
