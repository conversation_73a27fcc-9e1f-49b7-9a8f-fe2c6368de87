package dk.myckasP.mythicvaults.models;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.List;

public class CrateReward {
    private final ItemStack item;
    private final double chance;
    private final int quantity;

    public CrateReward(ItemStack item, double chance, int quantity) {
        this.item = item.clone();
        this.chance = chance;
        this.quantity = quantity;
    }

    public CrateReward(Material material, String displayName, List<String> lore, double chance, int quantity) {
        this.item = new ItemStack(material, 1); // Always create with amount 1 for display
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            if (displayName != null) {
                meta.setDisplayName(displayName);
            }
            if (lore != null && !lore.isEmpty()) {
                meta.setLore(lore);
            }
            item.setItemMeta(meta);
        }
        this.chance = chance;
        this.quantity = quantity;
    }

    public ItemStack getItem() {
        ItemStack cloned = item.clone();
        cloned.setAmount(1); // Always return quantity 1
        return cloned;
    }

    public double getChance() {
        return chance;
    }

    public int getQuantity() {
        return quantity;
    }

    public ItemStack getDisplayItem() {
        return item.clone();
    }
}
