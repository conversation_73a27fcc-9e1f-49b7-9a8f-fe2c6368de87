package dk.myckasP.mythicvaults.managers;

import dk.myckasP.mythicvaults.MythicVaults;
import dk.myckasP.mythicvaults.enums.CrateType;
import org.bukkit.*;
import org.bukkit.entity.Firework;
import org.bukkit.entity.Player;
import org.bukkit.inventory.meta.FireworkMeta;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

/**
 * Manages visual effects for crate opening system including particles and fireworks
 */
public class VisualEffectsManager {
    private final MythicVaults plugin;
    private final Map<UUID, BukkitTask> activeEffects;
    private final Random random;

    public VisualEffectsManager(MythicVaults plugin) {
        this.plugin = plugin;
        this.activeEffects = new HashMap<>();
        this.random = new Random();
    }

    /**
     * Starts crate opening effects around the chest block
     * @param player The player opening the crate
     * @param location The location of the chest
     * @param crateType The type of crate being opened
     */
    public void startCrateOpeningEffects(Player player, Location location, CrateType crateType) {
        // Stop any existing effects for this player
        stopEffects(player);

        // Create particle effects based on crate type
        BukkitTask effectTask = new BukkitRunnable() {
            private int ticks = 0;
            private final int maxTicks = 200; // 10 seconds of effects

            @Override
            public void run() {
                if (ticks >= maxTicks || !player.isOnline()) {
                    activeEffects.remove(player.getUniqueId());
                    cancel();
                    return;
                }

                // Create circling particle effects around the chest
                createCirclingParticles(location, crateType, ticks);
                
                // Add some ambient particles above the chest
                createAmbientParticles(location, crateType);

                ticks++;
            }
        }.runTaskTimer(plugin, 0L, 1L); // Run every tick for smooth animation

        activeEffects.put(player.getUniqueId(), effectTask);
    }

    /**
     * Creates circling particle effects around the chest
     */
    private void createCirclingParticles(Location center, CrateType crateType, int ticks) {
        World world = center.getWorld();
        if (world == null) return;

        // Create multiple circles at different heights and radii
        for (int circle = 0; circle < 3; circle++) {
            double radius = 1.5 + (circle * 0.3);
            double height = 1.0 + (circle * 0.2);
            double speed = 0.1 + (circle * 0.02);

            for (int i = 0; i < 8; i++) {
                double angle = (ticks * speed + (i * Math.PI / 4) + (circle * Math.PI / 6)) % (2 * Math.PI);
                double x = center.getX() + 0.5 + Math.cos(angle) * radius;
                double z = center.getZ() + 0.5 + Math.sin(angle) * radius;
                double y = center.getY() + height;

                Location particleLocation = new Location(world, x, y, z);
                
                // Choose particle type based on crate type
                Particle particle = getParticleForCrateType(crateType);
                world.spawnParticle(particle, particleLocation, 1, 0, 0, 0, 0);
            }
        }
    }

    /**
     * Creates ambient particles above the chest
     */
    private void createAmbientParticles(Location center, CrateType crateType) {
        World world = center.getWorld();
        if (world == null) return;

        // Spawn random particles above the chest
        for (int i = 0; i < 3; i++) {
            double x = center.getX() + 0.5 + (random.nextGaussian() * 0.5);
            double y = center.getY() + 1.5 + (random.nextDouble() * 1.0);
            double z = center.getZ() + 0.5 + (random.nextGaussian() * 0.5);

            Location particleLocation = new Location(world, x, y, z);
            
            // Add some sparkle effects
            world.spawnParticle(Particle.ENCHANTED_HIT, particleLocation, 1, 0, 0.1, 0, 0.02);
            
            // Add crate-specific particles
            if (random.nextDouble() < 0.3) {
                Particle particle = getParticleForCrateType(crateType);
                world.spawnParticle(particle, particleLocation, 1, 0, 0.05, 0, 0.01);
            }
        }
    }

    /**
     * Gets the appropriate particle type for each crate type
     */
    private Particle getParticleForCrateType(CrateType crateType) {
        switch (crateType) {
            case COMMON:
                return Particle.HAPPY_VILLAGER;
            case UNCOMMON:
                return Particle.ENCHANTED_HIT;
            case RARE:
                return Particle.FLAME;
            case EPIC:
                return Particle.DRAGON_BREATH;
            case LEGENDARY:
                return Particle.END_ROD;
            case BOSS:
                return Particle.PORTAL;
            case EVENT:
                return Particle.TOTEM;
            default:
                return Particle.ENCHANTED_HIT;
        }
    }

    /**
     * Creates reward celebration effects when a player wins rewards
     * @param player The player who won the rewards
     * @param location The location of the chest
     * @param crateType The type of crate
     * @param rewardCount The number of rewards won
     */
    public void createRewardCelebrationEffects(Player player, Location location, CrateType crateType, int rewardCount) {
        // Stop opening effects
        stopEffects(player);

        // Launch fireworks
        launchCelebrationFireworks(location, crateType, rewardCount);
        
        // Create particle burst
        createParticleBurst(location, crateType);
        
        // Create ascending particle trail
        createAscendingParticleTrail(location, crateType);
    }

    /**
     * Launches celebration fireworks at the chest location
     */
    private void launchCelebrationFireworks(Location location, CrateType crateType, int rewardCount) {
        World world = location.getWorld();
        if (world == null) return;

        // Launch multiple fireworks based on crate rarity
        int fireworkCount = Math.min(rewardCount + 1, 3);
        
        for (int i = 0; i < fireworkCount; i++) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    // Spawn firework slightly above the chest
                    Location fireworkLocation = location.clone().add(0.5, 1.5, 0.5);
                    fireworkLocation.add(
                        (random.nextDouble() - 0.5) * 2, 
                        random.nextDouble() * 2, 
                        (random.nextDouble() - 0.5) * 2
                    );

                    Firework firework = world.spawn(fireworkLocation, Firework.class);
                    FireworkMeta meta = firework.getFireworkMeta();

                    // Configure firework based on crate type
                    FireworkEffect.Builder effectBuilder = FireworkEffect.builder();
                    
                    // Set colors based on crate type
                    Color[] colors = getColorsForCrateType(crateType);
                    effectBuilder.withColor(colors);
                    effectBuilder.withFade(Color.WHITE);
                    
                    // Set effect type based on crate rarity
                    FireworkEffect.Type effectType = getFireworkTypeForCrateType(crateType);
                    effectBuilder.with(effectType);
                    
                    // Add trail and flicker for higher tier crates
                    if (isHighTierCrate(crateType)) {
                        effectBuilder.trail(true);
                        effectBuilder.flicker(true);
                    }

                    meta.addEffect(effectBuilder.build());
                    meta.setPower(1);
                    firework.setFireworkMeta(meta);
                }
            }.runTaskLater(plugin, i * 10L); // Stagger fireworks
        }
    }

    /**
     * Creates a particle burst effect at the chest location
     */
    private void createParticleBurst(Location center, CrateType crateType) {
        World world = center.getWorld();
        if (world == null) return;

        Location burstCenter = center.clone().add(0.5, 1.0, 0.5);
        
        // Create radial particle burst
        for (int i = 0; i < 30; i++) {
            double angle = (2 * Math.PI * i) / 30;
            double radius = 2.0;
            
            double x = Math.cos(angle) * radius;
            double z = Math.sin(angle) * radius;
            double y = random.nextDouble() * 1.5;
            
            Location particleLocation = burstCenter.clone().add(x, y, z);
            
            // Spawn multiple particle types for the burst
            world.spawnParticle(Particle.FIREWORKS_SPARK, particleLocation, 3, 0.1, 0.1, 0.1, 0.1);
            world.spawnParticle(getParticleForCrateType(crateType), particleLocation, 2, 0.1, 0.1, 0.1, 0.05);
        }

        // Add central explosion effect
        world.spawnParticle(Particle.EXPLOSION_LARGE, burstCenter, 1, 0, 0, 0, 0);
    }

    /**
     * Creates an ascending particle trail effect
     */
    private void createAscendingParticleTrail(Location center, CrateType crateType) {
        new BukkitRunnable() {
            private int ticks = 0;
            private final int maxTicks = 40; // 2 seconds

            @Override
            public void run() {
                if (ticks >= maxTicks) {
                    cancel();
                    return;
                }

                World world = center.getWorld();
                if (world == null) {
                    cancel();
                    return;
                }

                // Create ascending spiral of particles
                double height = (ticks / (double) maxTicks) * 4.0; // Rise 4 blocks
                double radius = 1.0 - (ticks / (double) maxTicks) * 0.5; // Shrinking radius

                for (int i = 0; i < 4; i++) {
                    double angle = (ticks * 0.3 + (i * Math.PI / 2)) % (2 * Math.PI);
                    double x = center.getX() + 0.5 + Math.cos(angle) * radius;
                    double z = center.getZ() + 0.5 + Math.sin(angle) * radius;
                    double y = center.getY() + 1.0 + height;

                    Location particleLocation = new Location(world, x, y, z);
                    world.spawnParticle(getParticleForCrateType(crateType), particleLocation, 1, 0, 0, 0, 0);
                    world.spawnParticle(Particle.FIREWORKS_SPARK, particleLocation, 1, 0.1, 0.1, 0.1, 0.02);
                }

                ticks++;
            }
        }.runTaskTimer(plugin, 0L, 1L);
    }

    /**
     * Gets colors for fireworks based on crate type
     */
    private Color[] getColorsForCrateType(CrateType crateType) {
        switch (crateType) {
            case COMMON:
                return new Color[]{Color.WHITE, Color.GRAY};
            case UNCOMMON:
                return new Color[]{Color.GREEN, Color.LIME};
            case RARE:
                return new Color[]{Color.BLUE, Color.AQUA};
            case EPIC:
                return new Color[]{Color.PURPLE, Color.FUCHSIA};
            case LEGENDARY:
                return new Color[]{Color.ORANGE, Color.YELLOW};
            case BOSS:
                return new Color[]{Color.RED, Color.MAROON};
            case EVENT:
                return new Color[]{Color.YELLOW, Color.WHITE, Color.ORANGE};
            default:
                return new Color[]{Color.WHITE};
        }
    }

    /**
     * Gets firework effect type based on crate type
     */
    private FireworkEffect.Type getFireworkTypeForCrateType(CrateType crateType) {
        switch (crateType) {
            case COMMON:
            case UNCOMMON:
                return FireworkEffect.Type.BALL;
            case RARE:
            case EPIC:
                return FireworkEffect.Type.BALL_LARGE;
            case LEGENDARY:
                return FireworkEffect.Type.BURST;
            case BOSS:
                return FireworkEffect.Type.STAR;
            case EVENT:
                return FireworkEffect.Type.CREEPER;
            default:
                return FireworkEffect.Type.BALL;
        }
    }

    /**
     * Checks if a crate type is considered high tier
     */
    private boolean isHighTierCrate(CrateType crateType) {
        return crateType == CrateType.LEGENDARY ||
               crateType == CrateType.BOSS ||
               crateType == CrateType.EVENT;
    }

    /**
     * Creates rolling animation effects during crate opening
     * @param player The player opening the crate
     * @param location The location of the chest
     * @param crateType The type of crate
     */
    public void createRollingEffects(Player player, Location location, CrateType crateType) {
        World world = location.getWorld();
        if (world == null) return;

        // Create pulsing effect around the chest during rolling
        new BukkitRunnable() {
            private int ticks = 0;
            private final int maxTicks = 60; // 3 seconds to match rolling animation

            @Override
            public void run() {
                if (ticks >= maxTicks || !player.isOnline()) {
                    cancel();
                    return;
                }

                // Create pulsing ring effect
                double pulse = Math.sin(ticks * 0.3) * 0.5 + 1.0; // Pulse between 0.5 and 1.5
                double radius = 1.2 * pulse;

                for (int i = 0; i < 12; i++) {
                    double angle = (i * Math.PI / 6);
                    double x = location.getX() + 0.5 + Math.cos(angle) * radius;
                    double z = location.getZ() + 0.5 + Math.sin(angle) * radius;
                    double y = location.getY() + 0.5;

                    Location particleLocation = new Location(world, x, y, z);
                    world.spawnParticle(Particle.ENCHANTED_HIT, particleLocation, 1, 0, 0, 0, 0);
                }

                // Add some upward floating particles
                if (ticks % 5 == 0) {
                    Location floatLocation = location.clone().add(
                        0.5 + (random.nextGaussian() * 0.3),
                        1.0,
                        0.5 + (random.nextGaussian() * 0.3)
                    );
                    world.spawnParticle(getParticleForCrateType(crateType), floatLocation, 1, 0, 0.1, 0, 0.02);
                }

                ticks++;
            }
        }.runTaskTimer(plugin, 0L, 1L);
    }

    /**
     * Creates a small celebration effect when an individual reward is won
     * @param player The player who won the reward
     * @param location The location of the chest
     * @param crateType The type of crate
     */
    public void createIndividualRewardEffect(Player player, Location location, CrateType crateType) {
        World world = location.getWorld();
        if (world == null) return;

        // Create a small burst of particles at the chest
        Location effectLocation = location.clone().add(0.5, 1.0, 0.5);

        // Spawn particles in a small burst
        for (int i = 0; i < 8; i++) {
            double angle = (2 * Math.PI * i) / 8;
            double radius = 0.8;

            double x = Math.cos(angle) * radius;
            double z = Math.sin(angle) * radius;
            double y = random.nextDouble() * 0.5;

            Location particleLocation = effectLocation.clone().add(x, y, z);
            world.spawnParticle(Particle.HAPPY_VILLAGER, particleLocation, 1, 0.1, 0.1, 0.1, 0.02);
            world.spawnParticle(getParticleForCrateType(crateType), particleLocation, 1, 0.05, 0.05, 0.05, 0.01);
        }

        // Add a small upward burst
        world.spawnParticle(Particle.FIREWORKS_SPARK, effectLocation, 5, 0.2, 0.2, 0.2, 0.1);
    }

    /**
     * Stops all active effects for a player
     * @param player The player to stop effects for
     */
    public void stopEffects(Player player) {
        BukkitTask task = activeEffects.remove(player.getUniqueId());
        if (task != null && !task.isCancelled()) {
            task.cancel();
        }
    }

    /**
     * Stops all active effects
     */
    public void stopAllEffects() {
        for (BukkitTask task : activeEffects.values()) {
            if (task != null && !task.isCancelled()) {
                task.cancel();
            }
        }
        activeEffects.clear();
    }
}
