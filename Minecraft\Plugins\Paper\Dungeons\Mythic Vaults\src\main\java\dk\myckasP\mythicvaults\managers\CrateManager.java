package dk.myckasP.mythicvaults.managers;

import dk.myckasP.mythicvaults.MythicVaults;
import dk.myckasP.mythicvaults.enums.CrateType;
import dk.myckasP.mythicvaults.models.Crate;
import dk.myckasP.mythicvaults.models.CrateReward;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.*;

public class CrateManager {
    private final MythicVaults plugin;
    private final Map<Location, Crate> crates;
    private File cratesFile;
    private FileConfiguration cratesConfig;

    public CrateManager(MythicVaults plugin) {
        this.plugin = plugin;
        this.crates = new HashMap<>();
        setupCratesFile();
    }

    private void setupCratesFile() {
        cratesFile = new File(plugin.getDataFolder(), "crates.yml");
        if (!cratesFile.exists()) {
            plugin.saveResource("crates.yml", false);
        }
        cratesConfig = YamlConfiguration.loadConfiguration(cratesFile);
    }

    public void forceUpdateCratesFile() {
        // Backup existing locations
        ConfigurationSection existingLocations = cratesConfig.getConfigurationSection("locations");

        // Force overwrite the crates.yml file with the new version
        plugin.saveResource("crates.yml", true);

        // Reload the configuration
        cratesConfig = YamlConfiguration.loadConfiguration(cratesFile);

        // Restore existing locations
        if (existingLocations != null) {
            cratesConfig.set("locations", existingLocations);
            try {
                cratesConfig.save(cratesFile);
            } catch (IOException e) {
                plugin.getLogger().severe("Could not save crates.yml after update: " + e.getMessage());
            }
        }

        plugin.getLogger().info("Force updated crates.yml configuration file!");
    }

    public void loadCrates() {
        crates.clear();
        
        // Load crate locations
        ConfigurationSection locationsSection = cratesConfig.getConfigurationSection("locations");
        if (locationsSection != null) {
            for (String key : locationsSection.getKeys(false)) {
                ConfigurationSection crateSection = locationsSection.getConfigurationSection(key);
                if (crateSection != null) {
                    Location location = deserializeLocation(crateSection.getConfigurationSection("location"));
                    CrateType type = CrateType.fromString(crateSection.getString("type"));
                    
                    if (location != null && type != null) {
                        Crate crate = new Crate(type, location);
                        loadCrateRewards(crate);
                        crates.put(location, crate);
                    }
                }
            }
        }
        
        plugin.getLogger().info("Loaded " + crates.size() + " crates!");

        // Create holograms for all loaded crates
        plugin.getHologramManager().recreateAllHolograms();
    }

    public void loadCrateRewards(Crate crate) {
        ConfigurationSection rewardsSection = cratesConfig.getConfigurationSection("rewards." + crate.getType().getConfigKey());
        if (rewardsSection != null) {
            int rewardCount = 0;
            for (String key : rewardsSection.getKeys(false)) {
                ConfigurationSection rewardSection = rewardsSection.getConfigurationSection(key);
                if (rewardSection != null) {
                    try {
                        Material material = Material.valueOf(rewardSection.getString("material", "STONE"));
                        String displayName = ChatColor.translateAlternateColorCodes('&',
                            rewardSection.getString("displayName", ""));
                        List<String> lore = rewardSection.getStringList("lore");
                        double chance = rewardSection.getDouble("chance", 1.0);
                        int quantity = rewardSection.getInt("quantity", 1);

                        // Translate color codes in lore
                        List<String> coloredLore = new ArrayList<>();
                        for (String line : lore) {
                            coloredLore.add(ChatColor.translateAlternateColorCodes('&', line));
                        }

                        CrateReward reward = new CrateReward(material, displayName, coloredLore, chance, quantity);
                        crate.addReward(reward);
                        rewardCount++;

                        // Debug logging for bundles
                        if (key.contains("bundle")) {
                            plugin.getLogger().info("Loaded bundle reward: " + key + " with material " + material + " and chance " + chance + "%");
                        }
                    } catch (IllegalArgumentException e) {
                        plugin.getLogger().warning("Invalid material for reward " + key + " in " + crate.getType().getConfigKey() + " crate: " + rewardSection.getString("material"));
                    }
                }
            }
            plugin.getLogger().info("Loaded " + rewardCount + " rewards for " + crate.getType().getConfigKey() + " crate");
        } else {
            plugin.getLogger().warning("No rewards section found for " + crate.getType().getConfigKey() + " crate");
        }
    }

    public void saveCrates() {
        // Clear existing locations
        cratesConfig.set("locations", null);
        
        int index = 0;
        for (Map.Entry<Location, Crate> entry : crates.entrySet()) {
            String path = "locations.crate" + index;
            cratesConfig.set(path + ".location", serializeLocation(entry.getKey()));
            cratesConfig.set(path + ".type", entry.getValue().getType().name());
            index++;
        }
        
        try {
            cratesConfig.save(cratesFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Could not save crates.yml: " + e.getMessage());
        }
    }

    public Crate getCrate(Location location) {
        return crates.get(location);
    }

    public void addCrate(Location location, CrateType type) {
        Crate crate = new Crate(type, location);
        loadCrateRewards(crate);
        crates.put(location, crate);
        saveCrates();

        // Create hologram for the new crate
        plugin.getHologramManager().createHologram(crate);
    }

    public void removeCrate(Location location) {
        // Remove hologram first
        plugin.getHologramManager().removeHologram(location);

        crates.remove(location);
        saveCrates();
    }

    public Set<Location> getCrateLocations() {
        return new HashSet<>(crates.keySet());
    }

    public Collection<Crate> getAllCrates() {
        return new ArrayList<>(crates.values());
    }

    public FileConfiguration getCratesConfig() {
        return cratesConfig;
    }

    private Map<String, Object> serializeLocation(Location location) {
        Map<String, Object> map = new HashMap<>();
        map.put("world", location.getWorld().getName());
        map.put("x", location.getBlockX());
        map.put("y", location.getBlockY());
        map.put("z", location.getBlockZ());
        return map;
    }

    private Location deserializeLocation(ConfigurationSection section) {
        if (section == null) return null;
        
        String worldName = section.getString("world");
        int x = section.getInt("x");
        int y = section.getInt("y");
        int z = section.getInt("z");
        
        if (worldName != null && plugin.getServer().getWorld(worldName) != null) {
            return new Location(plugin.getServer().getWorld(worldName), x, y, z);
        }
        return null;
    }
}
