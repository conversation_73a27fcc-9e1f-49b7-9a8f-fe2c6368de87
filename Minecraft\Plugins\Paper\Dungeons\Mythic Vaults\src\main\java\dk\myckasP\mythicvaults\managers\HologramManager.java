package dk.myckasP.mythicvaults.managers;

import dk.myckasP.mythicvaults.MythicVaults;
import dk.myckasP.mythicvaults.enums.CrateType;
import dk.myckasP.mythicvaults.models.Crate;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import org.bukkit.Location;
import org.bukkit.entity.Display;
import org.bukkit.entity.TextDisplay;
import org.bukkit.util.Transformation;
import org.joml.AxisAngle4f;
import org.joml.Vector3f;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class HologramManager {
    private final MythicVaults plugin;
    private final Map<Location, TextDisplay> holograms;
    
    public HologramManager(MythicVaults plugin) {
        this.plugin = plugin;
        this.holograms = new HashMap<>();
    }
    
    /**
     * Creates a hologram for the specified crate
     * @param crate The crate to create a hologram for
     */
    public void createHologram(Crate crate) {
        // Check if hologram is enabled in config
        if (!isHologramEnabled()) {
            return;
        }

        // Check if holograms are enabled for this specific crate type
        if (!isHologramEnabledForType(crate.getType())) {
            return;
        }
        
        Location crateLocation = crate.getLocation();
        
        // Remove existing hologram if present
        removeHologram(crateLocation);
        
        // Calculate hologram position (above the crate)
        double heightOffset = getHologramHeight();
        Location hologramLocation = crateLocation.clone().add(0.5, heightOffset, 0.5);
        
        // Create the TextDisplay entity
        TextDisplay hologram = crateLocation.getWorld().spawn(hologramLocation, TextDisplay.class, entity -> {
            // Set the hologram text
            Component text = createHologramText(crate);
            entity.text(text);
            
            // Configure display properties
            entity.setBillboard(Display.Billboard.CENTER); // Face the player
            entity.setBackgroundColor(org.bukkit.Color.fromARGB(0, 0, 0, 0)); // Transparent background
            entity.setSeeThrough(isSeeThrough()); // Configurable see-through
            entity.setShadowed(isShadowed()); // Configurable text shadow

            // Set transformation for proper scaling
            float scale = getHologramScale();
            Transformation transformation = new Transformation(
                new Vector3f(0, 0, 0), // no translation
                new AxisAngle4f(), // no left rotation
                new Vector3f(scale, scale, scale), // configurable scale
                new AxisAngle4f() // no right rotation
            );
            entity.setTransformation(transformation);

            // Make it persistent so it survives chunk unloads
            entity.setPersistent(true);

            // Set view range
            entity.setViewRange(getViewRange()); // Configurable view range
        });
        
        // Store the hologram
        holograms.put(crateLocation, hologram);
        
        plugin.getLogger().info("Created hologram for " + crate.getType().getDisplayName() + " at " + 
            crateLocation.getBlockX() + ", " + crateLocation.getBlockY() + ", " + crateLocation.getBlockZ());
    }
    
    /**
     * Removes the hologram at the specified location
     * @param location The location to remove the hologram from
     */
    public void removeHologram(Location location) {
        TextDisplay hologram = holograms.remove(location);
        if (hologram != null && hologram.isValid()) {
            hologram.remove();
            plugin.getLogger().info("Removed hologram at " + 
                location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ());
        }
    }
    
    /**
     * Removes all holograms
     */
    public void removeAllHolograms() {
        for (TextDisplay hologram : holograms.values()) {
            if (hologram != null && hologram.isValid()) {
                hologram.remove();
            }
        }
        holograms.clear();
        plugin.getLogger().info("Removed all holograms");
    }
    
    /**
     * Recreates all holograms for existing crates
     */
    public void recreateAllHolograms() {
        // First, clean up any existing holograms in the world that might be orphaned
        cleanupOrphanedHolograms();

        // Remove all tracked holograms
        removeAllHolograms();

        for (Crate crate : plugin.getCrateManager().getAllCrates()) {
            createHologram(crate);
        }
    }

    /**
     * Cleans up orphaned hologram entities in the world
     * This removes any TextDisplay entities near crate locations that might be leftover
     */
    private void cleanupOrphanedHolograms() {
        for (Crate crate : plugin.getCrateManager().getAllCrates()) {
            Location crateLocation = crate.getLocation();
            double heightOffset = getHologramHeight();
            Location hologramLocation = crateLocation.clone().add(0.5, heightOffset, 0.5);

            // Find and remove any TextDisplay entities near the hologram location
            crateLocation.getWorld().getNearbyEntities(hologramLocation, 0.5, 0.5, 0.5).forEach(entity -> {
                if (entity instanceof org.bukkit.entity.TextDisplay) {
                    entity.remove();
                }
            });
        }
    }
    
    /**
     * Creates the text component for the hologram
     * @param crate The crate to create text for
     * @return The formatted text component
     */
    private Component createHologramText(Crate crate) {
        CrateType type = crate.getType();

        // Create multi-line hologram similar to Hypixel style
        Component title = Component.text(type.getPlainDisplayName())
            .color(getHologramColor(type))
            .decoration(TextDecoration.BOLD, true);

        Component keyRequirement = Component.text("Requires " + type.getPlainDisplayName().replace(" Crate", " Key"))
            .color(NamedTextColor.YELLOW)
            .decoration(TextDecoration.BOLD, false);

        Component clickToOpen = Component.text("Right-click to open!")
            .color(NamedTextColor.GREEN)
            .decoration(TextDecoration.BOLD, false);

        // Only show description for boss and event crates
        if (type == CrateType.BOSS || type == CrateType.EVENT) {
            Component description = Component.text(getCrateDescription(type))
                .color(NamedTextColor.GRAY)
                .decoration(TextDecoration.BOLD, false);

            // Combine all components with newlines for boss and event
            return title
                .append(Component.newline())
                .append(description)
                .append(Component.newline())
                .append(keyRequirement)
                .append(Component.newline())
                .append(clickToOpen);
        } else {
            // For other crates, only show title, key requirement, and click to open
            return title
                .append(Component.newline())
                .append(keyRequirement)
                .append(Component.newline())
                .append(clickToOpen);
        }
    }

    /**
     * Gets the description for a crate type
     * @param type The crate type
     * @return The description text
     */
    private String getCrateDescription(CrateType type) {
        switch (type) {
            case COMMON:
                return "Basic rewards for new adventurers";
            case UNCOMMON:
                return "Keys, pouches, and valuable bundles";
            case RARE:
                return "Enhanced gear and rare materials";
            case EPIC:
                return "Powerful equipment and enchantments";
            case LEGENDARY:
                return "Legendary items and exclusive rewards";
            case BOSS:
                return "Ultimate boss rewards";
            case EVENT:
                return "Limited-time rewards";
            default:
                return "Mysterious rewards await inside";
        }
    }
    
    /**
     * Gets the color for the hologram based on crate type
     * @param crateType The crate type
     * @return The color for the hologram
     */
    private NamedTextColor getHologramColor(CrateType crateType) {
        return switch (crateType) {
            case COMMON -> NamedTextColor.WHITE;
            case UNCOMMON -> NamedTextColor.GREEN;
            case RARE -> NamedTextColor.DARK_AQUA;
            case EPIC -> NamedTextColor.DARK_PURPLE;
            case LEGENDARY -> NamedTextColor.GOLD;
            case BOSS -> NamedTextColor.DARK_RED;
            case EVENT -> NamedTextColor.LIGHT_PURPLE;
        };
    }
    
    /**
     * Checks if holograms are enabled in the configuration
     * @return true if holograms are enabled
     */
    public boolean isHologramEnabled() {
        return plugin.getConfigManager().getConfig().getBoolean("holograms.enabled", true);
    }
    
    /**
     * Gets the hologram height offset from configuration
     * @return The height offset in blocks
     */
    private double getHologramHeight() {
        return plugin.getConfigManager().getConfig().getDouble("holograms.height-offset", 1.5);
    }
    
    /**
     * Gets the hologram text from configuration
     * @return The hologram text with placeholders
     */
    private String getHologramText() {
        return plugin.getConfigManager().getConfig().getString("holograms.text", "{crate_name}");
    }

    /**
     * Gets the hologram scale from configuration
     * @return The scale factor for the hologram
     */
    private float getHologramScale() {
        return (float) plugin.getConfigManager().getConfig().getDouble("holograms.scale", 1.2);
    }

    /**
     * Gets the view range from configuration
     * @return The view range in blocks
     */
    private float getViewRange() {
        return (float) plugin.getConfigManager().getConfig().getDouble("holograms.view-range", 32.0);
    }

    /**
     * Checks if holograms should be see-through from configuration
     * @return true if holograms should be see-through
     */
    private boolean isSeeThrough() {
        return plugin.getConfigManager().getConfig().getBoolean("holograms.see-through", false);
    }

    /**
     * Checks if holograms should have shadows from configuration
     * @return true if holograms should have shadows
     */
    private boolean isShadowed() {
        return plugin.getConfigManager().getConfig().getBoolean("holograms.shadowed", true);
    }

    /**
     * Checks if holograms are enabled for a specific crate type
     * @param type The crate type to check
     * @return true if holograms are enabled for this crate type
     */
    private boolean isHologramEnabledForType(CrateType type) {
        String path = "holograms.enabled-types." + type.getConfigKey();
        return plugin.getConfigManager().getConfig().getBoolean(path, true);
    }
    
    /**
     * Gets the hologram at the specified location
     * @param location The location to check
     * @return The hologram at the location, or null if none exists
     */
    public TextDisplay getHologram(Location location) {
        return holograms.get(location);
    }
    
    /**
     * Checks if a hologram exists at the specified location
     * @param location The location to check
     * @return true if a hologram exists at the location
     */
    public boolean hasHologram(Location location) {
        // First check our tracked holograms
        if (holograms.containsKey(location) && holograms.get(location).isValid()) {
            return true;
        }

        // If not tracked or invalid, check if there's actually a TextDisplay entity in the world
        double heightOffset = getHologramHeight();
        Location hologramLocation = location.clone().add(0.5, heightOffset, 0.5);

        boolean hasEntityInWorld = location.getWorld().getNearbyEntities(hologramLocation, 0.5, 0.5, 0.5)
            .stream()
            .anyMatch(entity -> entity instanceof org.bukkit.entity.TextDisplay);

        return hasEntityInWorld;
    }

    /**
     * Cleans up the hologram reference without removing the entity
     * Used when chunks unload to prevent memory leaks
     * @param location The location to clean up
     */
    public void cleanupHologramReference(Location location) {
        holograms.remove(location);
    }

    /**
     * Cleans up invalid hologram references
     * Should be called periodically to prevent memory leaks
     */
    public void cleanupInvalidHolograms() {
        holograms.entrySet().removeIf(entry -> {
            TextDisplay hologram = entry.getValue();
            return hologram == null || !hologram.isValid();
        });
    }
}
