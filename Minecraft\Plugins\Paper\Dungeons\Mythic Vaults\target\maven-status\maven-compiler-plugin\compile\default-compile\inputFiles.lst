C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\models\Crate.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\gui\PreviewGUI.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\listeners\PlayerJoinListener.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\utils\KeyUtils.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\managers\HologramManager.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\managers\CrateManager.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\listeners\HologramListener.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\managers\ConfigManager.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\commands\SetCrateCommand.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\gui\CrateGUI.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\commands\MythicVaultsCommand.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\managers\VisualEffectsManager.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\models\CrateReward.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\MythicVaults.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\commands\GiveKeyCommand.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\listeners\CrateInteractListener.java
C:\Users\<USER>\CascadeProjects\Minecraft\Plugins\Paper\Dungeons\Mythic Vaults\src\main\java\dk\myckasP\mythicvaults\enums\CrateType.java
