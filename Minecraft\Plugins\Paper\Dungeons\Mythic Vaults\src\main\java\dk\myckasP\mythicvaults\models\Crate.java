package dk.myckasP.mythicvaults.models;

import dk.myckasP.mythicvaults.enums.CrateType;
import org.bukkit.Location;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class Crate {
    private final CrateType type;
    private final Location location;
    private final List<CrateReward> rewards;
    private final Random random;

    public Crate(CrateType type, Location location) {
        this.type = type;
        this.location = location;
        this.rewards = new ArrayList<>();
        this.random = new Random();
    }

    public CrateType getType() {
        return type;
    }

    public Location getLocation() {
        return location;
    }

    public List<CrateReward> getRewards() {
        return new ArrayList<>(rewards);
    }

    public void addReward(CrateReward reward) {
        rewards.add(reward);
    }

    public void removeReward(CrateReward reward) {
        rewards.remove(reward);
    }

    public void clearRewards() {
        rewards.clear();
    }

    public List<ItemStack> getRandomRewards(int count) {
        List<ItemStack> selectedRewards = new ArrayList<>();
        List<CrateReward> availableRewards = new ArrayList<>(rewards);

        for (int i = 0; i < count && !availableRewards.isEmpty(); i++) {
            CrateReward selectedReward = getWeightedRandomReward(availableRewards);
            if (selectedReward != null) {
                selectedRewards.add(selectedReward.getItem());
                availableRewards.remove(selectedReward); // Prevent duplicate rewards
            }
        }

        return selectedRewards;
    }

    private CrateReward getWeightedRandomReward(List<CrateReward> availableRewards) {
        if (availableRewards.isEmpty()) {
            return null;
        }

        double totalWeight = availableRewards.stream()
                .mapToDouble(CrateReward::getChance)
                .sum();

        double randomValue = random.nextDouble() * totalWeight;
        double currentWeight = 0;

        for (CrateReward reward : availableRewards) {
            currentWeight += reward.getChance();
            if (randomValue <= currentWeight) {
                return reward;
            }
        }

        // Fallback to last reward if something goes wrong
        return availableRewards.get(availableRewards.size() - 1);
    }

    public List<ItemStack> getAllRewardItems() {
        List<ItemStack> items = new ArrayList<>();
        for (CrateReward reward : rewards) {
            items.add(reward.getDisplayItem());
        }
        return items;
    }

    /**
     * Gets random CrateReward objects (not just ItemStacks) for accessing percentage info
     * @param count Number of rewards to select
     * @return List of selected CrateReward objects
     */
    public List<CrateReward> getRandomCrateRewards(int count) {
        List<CrateReward> selectedRewards = new ArrayList<>();
        List<CrateReward> availableRewards = new ArrayList<>(rewards);

        for (int i = 0; i < count && !availableRewards.isEmpty(); i++) {
            CrateReward selectedReward = getWeightedRandomReward(availableRewards);
            if (selectedReward != null) {
                selectedRewards.add(selectedReward);
                availableRewards.remove(selectedReward); // Prevent duplicate rewards
            }
        }

        return selectedRewards;
    }
}
