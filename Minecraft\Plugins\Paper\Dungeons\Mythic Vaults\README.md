# Mythic Vaults

An advanced crate system designed for dungeon servers running Paper 1.21.8.

## Features

- **7 Different Crate Types**: Common, Uncommon, Rare, Epic, Legendary, Boss, and Event crates
- **Key Requirement System**: Players need specific keys to open each crate type
- **Interactive GUI**: 3-row chest interface with minimized gray glass borders and maximized clickable white glass panes
- **Scrolling Animation**: Visual item scrolling with rolling sound effects when clicking on white glass panes
- **Animation Protection**: Prevents inventory closing during active animations
- **Sound Effects**:
  - Key usage sounds when successfully opening crates
  - Rolling note sounds during animations (Hypixel-style)
  - XP pickup sounds for clicking
  - Level-up sounds for winning items
- **Preview System**: Left-click crates to preview their contents
- **Configurable Rewards**: Fully customizable reward system with weighted chances
- **Admin Commands**: Easy setup and management commands for operators

## Installation

1. Download the plugin JAR file
2. Place it in your server's `plugins` folder
3. Restart your server
4. Configure crates using the commands below

## Commands

### Player Commands
- **Left-click** a crate block to preview its contents
- **Right-click** a crate block to open it (requires the corresponding crate key)

### Admin Commands
- `/mythicvaults` or `/mv` - Main command with help
- `/mythicvaults help` - Show help message
- `/mythicvaults info` - Show plugin information
- `/mythicvaults list` - List all crate locations
- `/mythicvaults reload` - Reload configuration
- `/setcrate <type>` - Set the block you're looking at as a crate
- `/givekey <player> <type> [amount]` - Give crate keys to a player

### Available Crate Types
- `common` - Common Crate (White)
- `uncommon` - Uncommon Crate (Green) - **Currently configured**
- `rare` - Rare Crate (Blue)
- `epic` - Epic Crate (Purple)
- `legendary` - Legendary Crate (Gold)
- `boss` - Boss Crate (Dark Red)
- `event` - Event Crate (Light Purple)

## How to Use

### Setting Up Crates
1. Look at any block in your world
2. Run `/setcrate uncommon` (or any other crate type)
3. The block is now a crate that players can interact with

### Getting Keys
1. **Give Keys**: Use `/givekey <player> <type> [amount]` to give keys to players
2. **Win Keys**: Players can win keys from other crates (e.g., Uncommon Keys from Uncommon Crates)

### Opening Crates
1. **Preview**: Left-click the crate to see all possible rewards
2. **Open**: Right-click the crate with the required key in your inventory
3. **Key Consumption**: One key will be consumed and you'll hear a chime sound
4. **Play**: Click on any of the white glass panes (3 clicks total)
5. **Animation**: Watch the exciting rolling animation with sound effects
6. **Win**: Each click reveals a random reward from the crate
7. **Protection**: You cannot close the inventory during animations

## Uncommon Crate Rewards

The Uncommon crate currently contains:
- **Uncommon Key** (15% chance) - Required to open Uncommon Crates
- **Small Token Pouch** (20% chance each, 2 total) - Contains tokens
- **Medium Token Pouch** (10% chance) - Contains more tokens
- **Large Token Pouch** (5% chance) - Contains many tokens
- **Small Gold Pouch** (15% chance each, 2 total) - Contains gold
- **Medium Gold Pouch** (8% chance) - Contains more gold
- **Large Gold Pouch** (4% chance) - Contains much gold
- **Small XP Pouch** (12% chance each, 2 total) - Contains experience
- **Medium XP Pouch** (6% chance) - Contains more experience
- **Large XP Pouch** (3% chance) - Contains much experience

## Permissions

- `mythicvaults.*` - All permissions (default: op)
- `mythicvaults.admin` - Admin commands (default: op)
- `mythicvaults.use` - Use crates (default: true)
- `mythicvaults.preview` - Preview crate contents (default: true)

## Configuration

The plugin creates several configuration files:
- `config.yml` - Main plugin settings and messages
- `crates.yml` - Crate rewards and locations

## Version Information

When an operator joins the server, they will see:
> **Mythic Vaults version 1.2.6 has loaded by MyckasP**

## Support

This plugin was created by MyckasP for private server use. 

## Building

To build the plugin from source:
1. Ensure you have Maven installed
2. Run `mvn clean package` in the project directory
3. The JAR file will be created in the `target` folder
