package dk.myckasP.mythicvaults.commands;

import dk.myckasP.mythicvaults.MythicVaults;
import dk.myckasP.mythicvaults.enums.CrateType;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MythicVaultsCommand implements CommandExecutor, TabCompleter {
    private final MythicVaults plugin;

    public MythicVaultsCommand(MythicVaults plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("mythicvaults.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command!");
            return true;
        }

        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "help":
                sendHelpMessage(sender);
                break;
            case "reload":
                handleReload(sender);
                break;
            case "info":
                handleInfo(sender);
                break;
            case "list":
                handleList(sender);
                break;
            case "debug":
                handleDebug(sender, args);
                break;
            case "forceupdate":
                handleForceUpdate(sender);
                break;
            default:
                sender.sendMessage(ChatColor.RED + "Unknown subcommand. Use /mythicvaults help for help.");
                break;
        }

        return true;
    }

    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== Mythic Vaults Help ===");
        sender.sendMessage(ChatColor.YELLOW + "/mythicvaults help" + ChatColor.WHITE + " - Show this help message");
        sender.sendMessage(ChatColor.YELLOW + "/mythicvaults reload" + ChatColor.WHITE + " - Reload the plugin configuration");
        sender.sendMessage(ChatColor.YELLOW + "/mythicvaults info" + ChatColor.WHITE + " - Show plugin information");
        sender.sendMessage(ChatColor.YELLOW + "/mythicvaults list" + ChatColor.WHITE + " - List all crate locations");
        sender.sendMessage(ChatColor.YELLOW + "/mythicvaults debug <cratetype>" + ChatColor.WHITE + " - Debug rewards for a crate type");
        sender.sendMessage(ChatColor.YELLOW + "/mythicvaults forceupdate" + ChatColor.WHITE + " - Force update crates.yml from plugin resources");
        sender.sendMessage(ChatColor.YELLOW + "/setcrate <type>" + ChatColor.WHITE + " - Set the block you're looking at as a crate");
        sender.sendMessage(ChatColor.YELLOW + "/givekey <player> <type> [amount]" + ChatColor.WHITE + " - Give crate keys to a player");
        sender.sendMessage("");
        sender.sendMessage(ChatColor.GRAY + "Available crate types:");
        for (CrateType type : CrateType.values()) {
            sender.sendMessage(ChatColor.GRAY + "- " + type.getDisplayName());
        }
    }

    private void handleReload(CommandSender sender) {
        try {
            plugin.getConfigManager().reloadConfig();
            plugin.getCrateManager().loadCrates();
            sender.sendMessage(ChatColor.GREEN + "Mythic Vaults configuration reloaded successfully!");
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Error reloading configuration: " + e.getMessage());
            plugin.getLogger().severe("Error reloading configuration: " + e.getMessage());
        }
    }

    private void handleInfo(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== Mythic Vaults Information ===");
        sender.sendMessage(ChatColor.YELLOW + "Version: " + ChatColor.WHITE + plugin.getDescription().getVersion());
        sender.sendMessage(ChatColor.YELLOW + "Author: " + ChatColor.WHITE + "MyckasP");
        sender.sendMessage(ChatColor.YELLOW + "Total Crates: " + ChatColor.WHITE + plugin.getCrateManager().getAllCrates().size());
        sender.sendMessage(ChatColor.YELLOW + "Available Types: " + ChatColor.WHITE + CrateType.values().length);
    }

    private void handleList(CommandSender sender) {
        if (plugin.getCrateManager().getAllCrates().isEmpty()) {
            sender.sendMessage(ChatColor.YELLOW + "No crates have been set up yet.");
            return;
        }

        sender.sendMessage(ChatColor.GOLD + "=== Crate Locations ===");
        plugin.getCrateManager().getAllCrates().forEach(crate -> {
            String location = String.format("%s at %d, %d, %d",
                crate.getLocation().getWorld().getName(),
                crate.getLocation().getBlockX(),
                crate.getLocation().getBlockY(),
                crate.getLocation().getBlockZ());
            sender.sendMessage(crate.getType().getDisplayName() + ChatColor.WHITE + " - " + location);
        });
    }

    private void handleDebug(CommandSender sender, String[] args) {
        if (args.length != 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /mythicvaults debug <cratetype>");
            sender.sendMessage(ChatColor.GRAY + "Available types: " + Arrays.toString(CrateType.values()));
            return;
        }

        CrateType crateType = CrateType.fromString(args[1]);
        if (crateType == null) {
            sender.sendMessage(ChatColor.RED + "Invalid crate type: " + args[1]);
            sender.sendMessage(ChatColor.GRAY + "Available types: " + Arrays.toString(CrateType.values()));
            return;
        }

        // Create a temporary crate to load rewards
        dk.myckasP.mythicvaults.models.Crate tempCrate = new dk.myckasP.mythicvaults.models.Crate(crateType, null);
        plugin.getCrateManager().loadCrateRewards(tempCrate);

        sender.sendMessage(ChatColor.GOLD + "=== Debug: " + crateType.getDisplayName() + " Rewards ===");
        sender.sendMessage(ChatColor.YELLOW + "Total rewards loaded: " + ChatColor.WHITE + tempCrate.getRewards().size());

        if (tempCrate.getRewards().isEmpty()) {
            sender.sendMessage(ChatColor.RED + "No rewards found! Check your crates.yml configuration.");
            return;
        }

        sender.sendMessage(ChatColor.YELLOW + "Reward details:");
        for (dk.myckasP.mythicvaults.models.CrateReward reward : tempCrate.getRewards()) {
            String itemName = reward.getDisplayItem().getItemMeta() != null ?
                reward.getDisplayItem().getItemMeta().getDisplayName() :
                reward.getDisplayItem().getType().name();
            sender.sendMessage(ChatColor.WHITE + "- " + itemName + ChatColor.GRAY + " (" +
                reward.getDisplayItem().getType() + ", " + reward.getChance() + "% chance)");
        }
    }

    private void handleForceUpdate(CommandSender sender) {
        try {
            plugin.getCrateManager().forceUpdateCratesFile();
            plugin.getCrateManager().loadCrates();
            sender.sendMessage(ChatColor.GREEN + "Force updated crates.yml and reloaded crate configurations!");
            sender.sendMessage(ChatColor.YELLOW + "This overwrote your crates.yml with the latest version from the plugin.");
            sender.sendMessage(ChatColor.YELLOW + "Use '/mythicvaults debug uncommon' to verify the bundles are now loaded.");
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Error force updating configuration: " + e.getMessage());
            plugin.getLogger().severe("Error force updating configuration: " + e.getMessage());
        }
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!sender.hasPermission("mythicvaults.admin")) {
            return new ArrayList<>();
        }

        if (args.length == 1) {
            return Arrays.asList("help", "reload", "info", "list", "debug", "forceupdate");
        }

        if (args.length == 2 && args[0].equalsIgnoreCase("debug")) {
            List<String> completions = new ArrayList<>();
            String input = args[1].toLowerCase();

            for (CrateType type : CrateType.values()) {
                if (type.name().toLowerCase().startsWith(input)) {
                    completions.add(type.name().toLowerCase());
                }
            }
            return completions;
        }

        return new ArrayList<>();
    }
}
