name: MythicVaults
version: '${project.version}'
main: dk.myckasP.mythicvaults.MythicVaults
api-version: '1.20'
prefix: MythicVaults
authors: [MyckasP]
description: Advanced crate system for dungeon servers
website: https://myckasP.dk

commands:
  mythicvaults:
    description: Main command for Mythic Vaults
    aliases: [mv, vaults, crates]
    permission: mythicvaults.admin
    usage: /<command> [help|reload|setcrate|preview|givekey]
  
  setcrate:
    description: Set a block as a crate
    permission: mythicvaults.admin
    usage: /<command> <cratetype>

  givekey:
    description: Give a crate key to a player
    permission: mythicvaults.admin
    usage: /<command> <player> <cratetype> [amount]

permissions:
  mythicvaults.*:
    description: Gives access to all MythicVaults permissions
    children:
      mythicvaults.admin: true
      mythicvaults.use: true
      mythicvaults.preview: true
      mythicvaults.delete: true
    default: op

  mythicvaults.admin:
    description: Allows access to admin commands
    default: op

  mythicvaults.use:
    description: Allows using crates
    default: true

  mythicvaults.preview:
    description: Allows previewing crate contents
    default: true

  mythicvaults.delete:
    description: Allows deleting crates with shift+left-click
    default: op
