package dk.myckasP.mythicvaults.enums;

import org.bukkit.ChatColor;

public enum CrateType {
    COMMON("Common Crate", ChatColor.WHITE, "common"),
    UNCOMMON("Uncommon Crate", ChatColor.GREEN, "uncommon"),
    RARE("Rare Crate", ChatColor.DARK_AQUA, "rare"),
    EPIC("Epic Crate", ChatColor.DARK_PURPLE, "epic"),
    LEGENDARY("Legendary Crate", ChatColor.GOLD, "legendary"),
    BOSS("Boss Crate", ChatColor.DARK_RED, "boss"),
    EVENT("Event Crate", ChatColor.LIGHT_PURPLE, "event");

    private final String displayName;
    private final ChatColor color;
    private final String configKey;

    CrateType(String displayName, ChatColor color, String configKey) {
        this.displayName = displayName;
        this.color = color;
        this.configKey = configKey;
    }

    public String getDisplayName() {
        return color + displayName;
    }

    public String getPlainDisplayName() {
        return displayName;
    }

    public ChatColor getColor() {
        return color;
    }

    public String getConfigKey() {
        return configKey;
    }

    public static CrateType fromString(String name) {
        for (CrateType type : values()) {
            if (type.name().equalsIgnoreCase(name) || 
                type.getPlainDisplayName().equalsIgnoreCase(name) ||
                type.getConfigKey().equalsIgnoreCase(name)) {
                return type;
            }
        }
        return null;
    }
}
