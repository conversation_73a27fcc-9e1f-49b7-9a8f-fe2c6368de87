package dk.myckasP.mythicvaults.listeners;

import dk.myckasP.mythicvaults.MythicVaults;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;

public class PlayerJoinListener implements Listener {
    private final MythicVaults plugin;

    public PlayerJoinListener(MythicVaults plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Check if player is an operator
        if (player.isOp()) {
            // Send version message to operator
            String version = plugin.getDescription().getVersion();
            player.sendMessage(ChatColor.GOLD + "Mythic Vaults " + ChatColor.YELLOW + "version " + 
                ChatColor.WHITE + version + ChatColor.YELLOW + " has loaded by " + 
                ChatColor.AQUA + "MyckasP");
        }
    }
}
